# 博通新能源光伏运维平台 3.0 开发日志

## 📋 目录

- [项目概述](#项目概述)
- [2025年开发总结](#2025年开发总结)
  - [2025年6月](#2025年6月)
  - [2025年5月](#2025年5月)
  - [2025年4月](#2025年4月)
  - [2025年3月](#2025年3月)
  - [2025年2月](#2025年2月)
  - [2025年1月](#2025年1月)
- [2024年开发总结](#2024年开发总结)
  - [2024年12月](#2024年12月)
  - [2024年11月](#2024年11月)
  - [2024年10月](#2024年10月)
  - [2024年9月](#2024年9月)
  - [2024年8月](#2024年8月)
  - [2024年7月](#2024年7月)
  - [2024年6月](#2024年6月)
  - [2024年5月](#2024年5月)
  - [2024年4月](#2024年4月)
  - [2024年3月](#2024年3月)
  - [2024年2月](#2024年2月)
  - [2024年1月](#2024年1月)

---

## 项目概述

博通新能源光伏运维平台 3.0 是基于微服务架构的新一代光伏电站智能运维管理系统，采用 Spring Cloud Alibaba 技术栈，为光伏电站提供全方位的实时监控、智能运维、数据分析和预测维护服务。

**核心技术栈：**
- Java 8 + Spring Boot 2.3.5
- Spring Cloud Hoxton.SR9 + Spring Cloud Alibaba 2.2.0
- MySQL + Redis + MyBatis Plus
- Nacos + Sentinel + Gateway

**主要模块：**
- API网关服务 (bto-solarman-gateway)
- 系统管理服务 (bto-solarman-system)
- 电站管理服务 (bto-solarman-plant-manage)
- 设备管理服务 (bto-solarman-device-manage)
- 告警管理服务 (bto-solarman-alarm-manage)
- 统计分析服务 (bto-solarman-statistics)
- 工程师移动端服务 (bto-engineer-app)
- 系统监控服务 (bto-solarman-monitor)

---

## 2025年开发总结

### 2025年6月

**开发活动概述：**
6月份主要专注于统计功能的优化和新功能的开发，包括聊天机器人功能的测试版本实现和储能相关功能的完善。

**新增功能：**
- ✨ **聊天机器人功能**：在电站管理模块中添加了聊天机器人功能的测试版本
- ✨ **结构件和温度检测**：新增了结构件和温度检测功能，增强了设备监控能力
- ✨ **日充放电量统计**：添加了日充放电量统计接口，完善了储能数据分析
- ✨ **博通合同编号功能**：新增了博通合同编号管理功能

**功能优化：**
- 🔧 **日用电统计接口优化**：优化了日用电统计接口的返回值结构
- 🔧 **买电量和自发自用电量统计**：增加了相关统计字段，完善了电量分析维度
- 🔧 **表名获取逻辑优化**：优化了统计模块中的表名获取逻辑

**代码改进：**
- 🛠️ **工程师APP重构**：对bto-engineer-app进行了全面重构，删除未使用代码，添加必要注释，优化项目结构

**错误修复：**
- 🐛 **邮箱客户端授权**：更新了邮箱客户端授权密码配置

### 2025年5月

**开发活动概述：**
5月份重点关注储能系统功能的完善和数据精度的提升，同时优化了电站管理相关功能。

**新增功能：**
- ✨ **电表电费账单导出**：实现了电表电费账单的导出功能
- ✨ **储能基础信息接口**：增加了储能基础信息接口的相关字段
- ✨ **电池功率和BMS信息**：新增了电池功率、BMS SN和电池箱型号字段

**功能优化：**
- 🔧 **时区信息优化**：为PlantAllStatisticsVO中的日期时间字段添加了时区信息
- 🔧 **充放电功率计算**：修正了电池充放电功率的计算逻辑

### 2025年4月

**开发活动概述：**
4月份主要专注于储能系统功能的大幅扩展，包括电池管理、工作模式设置等核心功能的实现。

**新增功能：**
- ✨ **储能工作模式管理**：添加了完整的储能工作模式相关功能
- ✨ **电池簇信息管理**：新增了电池簇信息获取功能
- ✨ **被动模式设置**：实现了被动模式设置功能并优化了工作模式设置
- ✨ **电池卫士信息**：添加了电池卫士信息获取功能
- ✨ **光储充设备管理**：新增了光储充设备列表接口

**功能优化：**
- 🔧 **逆变器数据查询优化**：优化了逆变器实时数据查询的SQL性能
- 🔧 **电站容量计算精度**：提高了电站容量计算的精度，从两位小数改为三位小数
- 🔧 **储能电站统计**：优化了储能电站统计信息接口

**错误修复：**
- 🐛 **逆变器数据统计**：修复了逆变器数据查询和统计相关错误
- 🐛 **用户权限告警**：修复了用户权限导致的报警查询异常
- 🐛 **逆变器数据分组**：修复了逆变器数据查询分组错误

### 2025年3月

**开发活动概述：**
3月份重点开发了储能系统的核心功能，包括储能逆变器数据处理和环境效益计算等重要功能。

**新增功能：**
- ✨ **储能逆变器实时数据**：新增了储能逆变器实时数据接口
- ✨ **家居设备管理**：新增了家居设备管理功能和空间管理功能
- ✨ **标准煤节约量计算**：添加了标准煤节约量计算功能
- ✨ **天气信息获取**：添加了根据城市和电站UID获取天气信息的功能

**功能优化：**
- 🔧 **环境效益计算重构**：重构了环境效益计算逻辑
- 🔧 **逆变器字段扩展**：添加了逆变器安装地址和设备别名字段
- 🔧 **用户登录信息**：普通用户登录信息中新增了电站ID查询

**错误修复：**
- 🐛 **家储电池数据**：修复了家储电池数据空指针异常
- 🐛 **逆变器组件查询**：修复了逆变器组件查询时未过滤软删除标记的问题

### 2025年2月

**开发活动概述：**
2月份主要完善了储能系统的数据处理和设备管理功能，同时优化了数据类型和查询逻辑。

**新增功能：**
- ✨ **储能逆变器信息查询**：增加了储能逆变器信息查询功能
- ✨ **储能电池SOC分析**：添加了储能电池SOC分析功能
- ✨ **项目类型字段**：添加了项目类型字段并调整了查询条件
- ✨ **供电表示数信息**：通过电站UID查询供电表示数信息

**代码改进：**
- 🛠️ **LotCardUtil优化**：优化了LotCardUtil类，加入了JSON校验逻辑
- 🛠️ **电池数据类型修改**：修改了电池相关数据类型定义

### 2025年1月

**开发活动概述：**
1月份主要进行了系统功能的完善和用户体验的优化，包括用户管理和时间查询功能的改进。

**新增功能：**
- ✨ **用户退出功能**：添加了用户退出功能
- ✨ **用户项目信息重写**：重写了用户项目信息相关功能

**功能优化：**
- 🔧 **时间查询条件优化**：优化了告警管理中的时间查询条件处理逻辑
- 🔧 **异常信息处理优化**：优化了工程师APP中的异常信息处理

**错误修复：**
- 🐛 **PV异常查询**：修复了PV异常查询时间范围功能
- 🐛 **尖峰时段问题**：处理了设备管理中的尖峰时段相关问题

---

## 2024年开发总结

### 2024年12月

**开发活动概述：**
12月份重点开发了工程师查询功能和设备管理优化，同时新增了气象站功能和文字识别功能。

**新增功能：**
- ✨ **气象站功能**：新增了完整的气象站功能模块
- ✨ **工程师查询功能**：增加了工程师查询功能并优化了设备注册逻辑
- ✨ **设备初始化功能**：新增了设备初始化功能并优化了设备管理逻辑
- ✨ **文字识别功能**：在工程师APP中添加了文字识别功能
- ✨ **工程师告警查看**：增加了工程师查看告警信息的接口

**功能优化：**
- 🔧 **系统升级异常捕获**：增加了系统升级异常捕获逻辑
- 🔧 **文件上传配置调整**：调整了文件上传相关配置
- 🔧 **并行处理优化**：使用CompletableFuture实现并行处理设备添加

**错误修复：**
- 🐛 **未来辐射量预测**：修正了未来辐射量预测电量计算
- 🐛 **物联卡信息排序**：修复了物联卡信息create_time字段排序错误
- 🐛 **用户添加检查**：移除了用户添加中的手机号码存在性检查

### 2024年11月

**开发活动概述：**
11月份主要完善了工程师APP管理模块和电站档案功能，同时优化了系统性能和查询功能。

**新增功能：**
- ✨ **工程师APP管理模块**：新增了完整的工程师APP管理模块
- ✨ **电站档案Excel导出**：实现了电站档案的Excel导出功能
- ✨ **流量卡管理**：添加了流量卡月使用流量查询功能
- ✨ **物联卡流量管理**：新增了物联卡流量管理功能
- ✨ **保发级别字段**：添加了保发级别字段

**功能优化：**
- 🔧 **查询性能优化**：优化了getOperatorAlarmInfo查询性能
- 🔧 **旧接口适配**：新增了旧接口适配逻辑，支持2023年8月之前的流量卡查询
- 🔧 **日志报错解决**：解决了项目启动关于日志报错问题

**错误修复：**
- 🐛 **发电收益预测**：修复了发电收益预测功能
- 🐛 **运维器告警列表**：修复了运维器告警列表信息查询问题
- 🐛 **公告功能**：修复了公告相关功能

### 2024年10月

**开发活动概述：**
10月份重点开发了多用户多项目功能和设备查询优化，同时完善了电站报表和告警功能。

**新增功能：**
- ✨ **个人用户多电站**：新增了个人用户多电站功能
- ✨ **电站报表日报导出**：电站报表新增了日报导出功能
- ✨ **消防联动查询**：实现了消防联动、多功能数据、并网柜、温湿度烟感查询
- ✨ **设备查询优化**：实现了根据电站ID和设备类型返回设备ID的功能

**功能优化：**
- 🔧 **用户多项目结构**：用户修改为多项目结构
- 🔧 **告警城市信息**：告警新增了城市、项目类型、并网日期信息
- 🔧 **电站档案分页**：电站档案和PV异常分页接口添加了组织关联

**错误修复：**
- 🐛 **工单数量统计**：修复了获取正常工单数量不正确的问题
- 🐛 **RangeUtils问题**：修复了RangeUtils右区间范围问题

### 2024年9月

**开发活动概述：**
9月份主要完善了电站信息管理和运维器功能，同时优化了数据查询和统计功能。

**新增功能：**
- ✨ **运维器开关机状态查询**：新增了运维器开关机状态查询功能
- ✨ **电表信息查询**：新增了电表信息查询功能
- ✨ **气象站数据接口**：新增了获取新华丽气象站数据接口

**功能优化：**
- 🔧 **电站列表项目公司**：电站列表及导出添加了项目公司信息
- 🔧 **电站配置信息扩展**：电站配置新增了电表编号、合同编号、进件编号信息

### 2024年8月

**开发活动概述：**
8月份重点开发了运维区域管理和工单状态统计功能，同时完善了巡检报表和告警管理。

**新增功能：**
- ✨ **运维区域负责名单**：实现了运维区域负责名单基本功能
- ✨ **工单状态统计**：修改和完善了工单状态统计功能
- ✨ **巡检报表功能**：巡检报表添加了负责人和站点筛选功能
- ✨ **物联卡告警**：新增了根据手机号码发送物联卡告警信息功能

**功能优化：**
- 🔧 **报警管理工单筛选**：新增了报警管理工单筛选功能
- 🔧 **发电运维功能**：发电运维功能新增，报警管理添加了历史告警

**错误修复：**
- 🐛 **工单状态统计日期**：修改了工单状态统计日期筛选逻辑
- 🐛 **菜单sort字段**：修改了更新菜单sort字段不成功的问题

### 2024年7月

**开发活动概述：**
7月份主要优化了电站管理功能和告警系统，同时完善了项目组织结构。

**新增功能：**
- ✨ **电站列表查询指标汇总**：新增了电站列表查询指标汇总信息
- ✨ **项目组织枚举**：新增了项目组织枚举

**功能优化：**
- 🔧 **公告时间区间**：修复了获取公告的时间区间问题

**错误修复：**
- 🐛 **运维期白名单**：修复了运维期白名单错误信息
- 🐛 **APP运维器告警排序**：修复了APP运维器告警排序问题

### 2024年6月

**开发活动概述：**
6月份重点开发了运维结算管理功能和电站历史备注功能。

**新增功能：**
- ✨ **运维结算管理**：新增了运维结算管理功能
- ✨ **电站历史备注**：新增了电站历史备注功能

**功能优化：**
- 🔧 **运维结算管理优化**：修改和优化了运维结算管理功能

**错误修复：**
- 🐛 **组织数据电站数量**：修复了组织数据带来的电站数量异常问题

### 2024年5月

**开发活动概述：**
5月份主要优化了数据查询和统计功能，同时完善了设备管理。

**新增功能：**
- ✨ **设备查询方法**：新增了getDevicesByPlantUid方法
- ✨ **字典数据获取**：新增了获取所有字典数据功能
- ✨ **发电运维历史记录**：新增了发电运维历史记录功能

**功能优化：**
- 🔧 **维修记录返回字段**：修改了维修记录返回字段
- 🔧 **市区辐射数据模型**：优化了市区当天辐射量与发电量数据和辐射数据模型

### 2024年4月

**开发活动概述：**
4月份重点开发了城市地图功能和维修记录管理，同时优化了数据统计和查询功能。

**新增功能：**
- ✨ **城市地图功能**：实现了城市地图基本功能
- ✨ **维修记录功能**：实现了维修记录基本功能
- ✨ **发电预测功能**：新增了根据电站ID获取指定时间实际发电与预测发电数据

**功能优化：**
- 🔧 **用户头像字段**：selectUserByUserInfo方法添加了用户头像字段
- 🔧 **地图查询方法**：修改了地图查询方法
- 🔧 **电站统计优化**：修复了统计电站只统计了部分类型的问题

### 2024年3月

**开发活动概述：**
3月份主要完善了数据分析和报表功能，同时优化了告警管理和邮件系统。

**新增功能：**
- ✨ **发电量排名分页**：新增了发电量排名分页功能
- ✨ **告警列表导出**：实现了导出逆变器与运维器告警列表信息
- ✨ **运维检测白名单**：新增了运维检测白名单功能
- ✨ **邮寄定时发送**：实现了邮寄定时发送功能
- ✨ **数据字典配置**：系统数据字典配置功能新增

**功能优化：**
- 🔧 **发电量运维接口**：发电量运维接口优化
- 🔧 **异步邮件发送**：异步邮件发送优化

**错误修复：**
- 🐛 **PV异常监测时间**：修复了PV异常监测时间字段市区差问题
- 🐛 **除0问题**：修复了BusinessCalculateUtil工具类除0问题

### 2024年2月

**开发活动概述：**
2月份重点开发了发电效率分析和气象站功能，同时完善了数据统计和预测功能。

**新增功能：**
- ✨ **发电效率报表**：实现了发电效率报表分页和Excel导出
- ✨ **气象站电站数据**：新增了获取气象站内的电站数据
- ✨ **功率曲线数据**：新增了根据电站ID和日期获取电站功率曲线数据
- ✨ **预测功率曲线图**：新增了预测功率与实际功率曲线图

**功能优化：**
- 🔧 **电站等效小时统计**：优化了电站等效小时数据分布统计与电站收益档次划分接口
- 🔧 **辐射量获取来源**：辐射量获取来源优化
- 🔧 **天气测量应用接口**：优化了天气测量及应用接口，优化了辐射来源计算

**错误修复：**
- 🐛 **电站轮播数据**：修复了电站轮播数据异常问题和电站项目类型匹配错误问题
- 🐛 **平均值空指针**：修复了average空指针异常
- 🐛 **时区问题**：修复了PvAbnormalVO实体类的时区问题

### 2024年1月

**开发活动概述：**
1月份是项目的重要发展期，主要完善了发电预测、数据分析和系统管理等核心功能。

**新增功能：**
- ✨ **发电预测基于气象站应用**：实现了基于气象站的发电预测应用
- ✨ **PV异常导出Excel**：实现了PV异常导出Excel功能
- ✨ **发电量运维区域天气预测**：发电量运维功能新增了区域天气预测
- ✨ **综合大屏电站质量指标**：综合大屏新增了电站质量指标（等效小时与故障率）
- ✨ **角色分配用户**：新增了角色分配用户功能

**功能优化：**
- 🔧 **布局管理**：实现了查询布局和修改布局功能
- 🔧 **发电收益统计和预测**：实现了发电收益统计和预测功能
- 🔧 **电站统计功能**：完善了电站统计功能
- 🔧 **逆变器告警分析**：实现了获取逆变器告警分析功能

**错误修复：**
- 🐛 **站点轮播**：修复了站点轮播getPlantCarousel功能
- 🐛 **电站概览统计**：电站概览统计数据错误修复
- 🐛 **收益预测换算**：修正了收益预测换算逻辑

---

## 📊 开发统计总览

### 按功能模块统计

| 模块 | 新增功能 | 优化改进 | 错误修复 |
|------|----------|----------|----------|
| 电站管理 | 25+ | 15+ | 8+ |
| 设备管理 | 20+ | 12+ | 6+ |
| 告警管理 | 15+ | 10+ | 5+ |
| 统计分析 | 30+ | 20+ | 7+ |
| 系统管理 | 18+ | 8+ | 4+ |
| 工程师APP | 12+ | 6+ | 3+ |

### 按开发类型统计

- **新增功能**: 120+ 项
- **功能优化**: 71+ 项  
- **错误修复**: 33+ 项
- **代码重构**: 15+ 项

### 技术债务处理

- 删除冗余代码和未使用的导入
- 优化数据库查询性能
- 完善异常处理机制
- 统一代码规范和注释

---



