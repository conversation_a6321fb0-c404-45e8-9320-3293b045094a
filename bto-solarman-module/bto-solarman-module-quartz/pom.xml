<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bto</groupId>
        <artifactId>bto-solarman-module</artifactId>
        <version>3.0.0</version>
    </parent>

    <artifactId>bto-solarman-module-quartz</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.bto</groupId>
            <artifactId>commons-core</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bto</groupId>
            <artifactId>commons-api</artifactId>
            <version>3.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bto</groupId>
            <artifactId>commons-redis</artifactId>
            <version>3.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bto</groupId>
            <artifactId>commons-sms</artifactId>
            <version>3.0.0</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

</project>