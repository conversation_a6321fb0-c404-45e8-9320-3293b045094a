package com.bto.plant.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.handler.MapResultHandler;
import com.bto.commons.pojo.dto.CounterQuery;
import com.bto.commons.pojo.entity.CounterEntity;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/7 15:53
 */
@Mapper
public interface CounterDao extends BaseMapper<CounterEntity> {
    Page<CounterEntity> getPage(@Param("page") Page<CounterEntity> page, @Param("query") CounterQuery query, @Param("userInfo") RequireParamsDTO userInfo);

    List<String> getDeviceIdsById(@Param("cabinetId") List<String> cabinetId);
    void getMap(@Param("cabinetId") List<String> cabinetId, MapResultHandler<String, String> mapResultHandler);
}
