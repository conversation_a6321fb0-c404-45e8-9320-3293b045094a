package com.bto.plant.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.commons.pojo.entity.CityMapEntity;
import com.bto.commons.pojo.vo.CityMapVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 城市地图
*
* <AUTHOR> 
* @since  2024-04-17
*/
@Mapper
public interface CityMapDao extends BaseMapper<CityMapEntity> {

    List<CityMapVO> getParentAndChild(@Param("cities") List<String> cities);
}