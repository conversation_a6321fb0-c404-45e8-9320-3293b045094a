package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.commons.constant.DeviceType;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.FireFightingQuery;
import com.bto.commons.pojo.entity.FireFightingEntity;
import com.bto.commons.pojo.entity.HourTownEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.dao.FireFightingDao;
import com.bto.plant.service.FireFightingService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class FireFightingServiceImpl extends ServiceImpl<FireFightingDao, FireFightingEntity> implements FireFightingService {
    public final GlobalParamUtil globalParamUtil;
    public final DeviceServiceClient deviceServiceClient;

    @Override
    public PageResult<FireFightingEntity> page(FireFightingQuery query) {
        Result<List<String>> result = deviceServiceClient.getListByType(query.getPlantUid(), DeviceType.FIRE_FIGHTING.getCode());
        if (result.getStatus().equals(ResultEnum.SUCCESS.getCode())){
            List<String> ids = result.getData(new TypeReference<List<String>>() {
            });
            if (CollUtil.isEmpty(ids)){
                throw new BusinessException("电站不存在消防联动设备");
            }
            query.setId(ids);
        }
        Page<FireFightingEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        page = baseMapper.page(page, query, null);

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    private LambdaQueryWrapper<FireFightingEntity> getWrapper(FireFightingQuery query) {
        LambdaQueryWrapper<FireFightingEntity> wrapper = Wrappers.lambdaQuery();
        wrapper
                .orderByDesc(FireFightingEntity::getUpdateTime)
        ;

        return wrapper;
    }


}