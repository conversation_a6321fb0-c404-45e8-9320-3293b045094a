package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.commons.constant.OrientationEnum;
import com.bto.commons.constant.ProjectTypeEnum;
import com.bto.commons.enums.WindEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.dto.PlantVO;
import com.bto.commons.pojo.entity.MeteorologyEntity;
import com.bto.commons.pojo.entity.PlantInfoEntity;
import com.bto.commons.pojo.entity.WeatherEntity;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.commons.utils.DateUtils;
import com.bto.commons.utils.RangeUtils;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.convert.ForecastBaseInfoConvert;
import com.bto.plant.convert.PlantAddressConvert;
import com.bto.plant.convert.PlantInfoConvert;
import com.bto.plant.dao.*;
import com.bto.plant.service.*;
import com.bto.redis.utils.RedisUtil;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bto.commons.constant.RedisKey.*;
import static com.bto.commons.response.ResultEnum.DATETIME_FORMAT_FAILED;

/**
 * <AUTHOR> by zhb on 2023/12/21.
 */

@Service
@AllArgsConstructor

public class ForecastServiceImpl implements ForecastService {

    private final MeteorologyMapper meteorologyMapper;
    private final WeatherRadiationService weatherRadiationService;
    private final WeatherMapper weatherMapper;
    private final ForecastMapper forecastMapper;
    private final BtoPlantInfoService plantInfoService;
    private final PlantService plantService;
    private final WeatherFutureService weatherFutureService;
    private final PlantMapper plantMapper;
    private final GlobalParamUtil globalParamUtil;
    private final RadiantQuantityDao radiantQuantityDao;
    private final RedisUtil redisUtil;
    private final DeviceServiceClient deviceServiceClient;
    private static final BigDecimal ELECTRICITY_BENEFIT_MULTIPLIER = new BigDecimal(0.45).setScale(2, RoundingMode.HALF_UP);

    private final WeatherStationService weatherStationService;
    private final CarouselService carouselService;
    private final RadiantQuantityService radiantQuantityService;

    @Override
    @DS("slave")
    public List<MeteorologyEntity> getMeteorologyListByPlantId(String plantId, String begin, String end) {
        String weatherStationUid = forecastMapper.getWeatherStationUid(plantId);
        if (StrUtil.isEmpty(weatherStationUid)) {
            throw new BusinessException("电站ID：" + plantId + "所在地无气象站，无法提供发电预测数据");
        }
        return meteorologyMapper.selectByCriteria(begin, end, weatherStationUid);
    }

    @Override
    public List<RealityForecastElectricityVO> getRealityForecastElectricity(RealityForecastElectricityDTO query) {
        return forecastMapper.getRealityForecastElectricity(query);
    }


    @Override
    @DS("slave")
    public Double getRadiationByAddress(PlantAddressDTO plantAddressDTO, Date dateTime) {
        String city = plantAddressDTO.getCity();
        String begin = DateUtils.getDayStart(dateTime);
        String end = DateUtils.getDayEnd(dateTime);
        if (StrUtil.isEmpty(city)) {
            throw new BusinessException("电站地址信息缺失，无法获取天气信息");
        }
        String time = DateUtils.getCurrentMonth(dateTime);
        List<WeatherEntity> weatherEntities = weatherMapper.selectList("bto_weather_" + time, begin, end, plantAddressDTO.getCity());
        List<WeatherRadiationVO> weatherRadiations = weatherMapper.selectWeatherRadiation("bto_weather_" + time, begin, end, city);
        return acquiredRadiation(weatherEntities, weatherRadiations);
    }

    @Override
    @DS("slave")
    public Double getRadiationByCity(String city, Date dateTime) {
        String dateStr = DateUtil.format(dateTime, "yyyy-MM-dd");
        String time = DateUtils.getCurrentMonth(dateTime);
        return weatherMapper.getRadiationByCity("bto_weather_" + time, dateStr + "%", city);
    }

    @Override
    public BatteryDivinerVO getFutureForecastData(String plantId, Double radiation, Date dateTime) {
        String futureForecastData = forecastMapper.getFutureForecastData(plantId, radiation);
        if (Objects.isNull(futureForecastData)) {
            throw new BusinessException("电站ID：" + plantId + "所在地无气象站，无法提供发电预测数据");
        }
        BatteryDivinerVO batteryDiviner = new BatteryDivinerVO();
        batteryDiviner.setElectricity(futureForecastData);
        batteryDiviner.setCollectDate(DateUtil.format(dateTime, "yyyy-MM-dd"));
        return batteryDiviner;
    }

    @Override
    public List<BatteryDivinerVO> getHistoryElectricityByCity(String city) {
        // 历史七天实际发电量
        return plantService.getElectricityByCity(city);
    }

    @Override
    public HashMap<String, List<DateRadiationVO>> getDateRadiationByCity(String city) {
        HashMap<String, List<DateRadiationVO>> hashMap = new HashMap<>();
        List<DateRadiationVO> futureRadiationList = new ArrayList<>();
        List<DateRadiationVO> historyFutureRadiation = new ArrayList<>();
        List<DateRadiationVO> historyRadiationList =new ArrayList<>();
        List<List<?>> predictElectricityByCity = forecastMapper.getPredictElectricityByCity(city);
        List<PredictRegionalElectricityVO> predictRegionalElectricityVos = (List<PredictRegionalElectricityVO>) predictElectricityByCity.get(1);
        List<RegionalElectricityVO> regionalElectricityVos = (List<RegionalElectricityVO>) predictElectricityByCity.get(0);
        Collections.reverse(regionalElectricityVos);
        for (PredictRegionalElectricityVO entity : predictRegionalElectricityVos) {
            DateRadiationVO dateRadiationVO = new DateRadiationVO(entity.getCollectDate(), entity.getFutureRadiant(), city);
            futureRadiationList.add(dateRadiationVO);
        }
        for (RegionalElectricityVO regionalElectricityVo : regionalElectricityVos) {
            DateRadiationVO dateRadiationVO = new DateRadiationVO(regionalElectricityVo.getCollectDate(), regionalElectricityVo.getPredictQuantity(), city);
            historyFutureRadiation.add(dateRadiationVO);
            DateRadiationVO dateRadiation = new DateRadiationVO(regionalElectricityVo.getCollectDate(), regionalElectricityVo.getRadiantQuantity(), city);
            historyRadiationList.add(dateRadiation);
        }
        historyFutureRadiation.remove(historyRadiationList.size() - 1);
        DateRadiationVO today = futureRadiationList.get(0);
        historyFutureRadiation.add(today);
        hashMap.put("futureRadiationList", futureRadiationList);
        hashMap.put("historyFutureRadiation", historyFutureRadiation);
        hashMap.put("historyRadiationList", historyRadiationList);
        return hashMap;
    }

    public List<LocalDate> getHistorySevenDaysDateList() {
        LocalDate today = LocalDate.now();
        // 创建一个日期流，从今天开始，往前推7天
        Stream<LocalDate> dateStream = Stream.iterate(today, date -> date.minusDays(1));
        List<LocalDate> historyDateList = dateStream.limit(7).collect(Collectors.toList());
        Collections.reverse(historyDateList);
        return historyDateList;
    }

    public List<WeatherFutureVO> getFutureSevenDayWeatherList(List<String> city, int size) {

        LocalDate today = LocalDate.now();
        // 往后推size天
        Stream<LocalDate> futureDateStream = Stream.iterate(today.plusDays(1), date -> date.plusDays(1));
        List<LocalDate> futureDateList = futureDateStream.limit(size).collect(Collectors.toList());
        return weatherFutureService.getFutureWeatherList(futureDateList, city);
    }


    @Override
    public String getFutureForecastByCity(String city, Double cityRadiation) {
        return forecastMapper.getFutureForecastByCity(city, cityRadiation);
    }

    @Override
    @DS("slave")
    public HashMap<String, List<WeatherFutureVO>> getWeatherByCity(String city) {
        HashMap<String, List<WeatherFutureVO>> hashMap = new HashMap<>();
        // 未来七天天气情况
        List<WeatherFutureVO> futureWeatherList = getFutureSevenDayWeatherList(Collections.singletonList(city), 7);
        hashMap.put("futureWeatherList", futureWeatherList);
        // 历史七天天气情况
        List<LocalDate> historySevenDaysDateList = getHistorySevenDaysDateList();
        // 判断第一天和最后一天是否为同月，不同月需要分表查询
        LocalDate firstDay = historySevenDaysDateList.get(0);
        LocalDate lastDay = historySevenDaysDateList.get(historySevenDaysDateList.size() - 1);
        String startOfDay = firstDay.atStartOfDay().toString();
        String lastOfDay = lastDay.atTime(23, 59, 59).toString();
        if (firstDay.getMonth().equals(lastDay.getMonth())) {
            // 同月份
            List<WeatherFutureVO> historySevenDaysWeather = weatherMapper.getHistorySevenDaysWeather("bto_weather_" + DateUtils.getCurrentMonth(lastDay), historySevenDaysDateList, city, startOfDay, lastOfDay);
            hashMap.put("historySevenDaysWeather", historySevenDaysWeather);
        } else {
            // 不同月份
            int crossMonthIndexes = 0;
            for (int i = 0; i < historySevenDaysDateList.size() - 1; i++) {
                LocalDate currentDate = historySevenDaysDateList.get(i);
                LocalDate nextDate = historySevenDaysDateList.get(i + 1);
                if (currentDate.getMonthValue() != nextDate.getMonthValue()) {
                    // 记录跨月份日期的索引
                    crossMonthIndexes = i;
                }
            }
            //     上个月数据
            LocalDate lastMonth = historySevenDaysDateList.get(crossMonthIndexes);
            String lastMonthOfDay = lastMonth.atTime(23, 59, 59).toString();
            List<LocalDate> lastMonthDate = historySevenDaysDateList.subList(0, crossMonthIndexes == 0 ? 1 : crossMonthIndexes);
            List<WeatherFutureVO> lastMonthWeather = weatherMapper.getHistorySevenDaysWeather("bto_weather_" + DateUtils.getCurrentMonth(lastMonth), lastMonthDate, city, startOfDay, lastMonthOfDay);
            //     下个月数据
            LocalDate monthOneDay = historySevenDaysDateList.get(crossMonthIndexes + 1);
            String monthOneDayStr = monthOneDay.atStartOfDay().toString();
            List<LocalDate> monthDate = historySevenDaysDateList.subList(crossMonthIndexes + 1, 6);
            List<WeatherFutureVO> monthDateWeather = weatherMapper.getHistorySevenDaysWeather("bto_weather_" + DateUtils.getCurrentMonth(monthOneDay), monthDate, city, monthOneDayStr, lastOfDay);
            //     汇总数据
            lastMonthWeather.addAll(monthDateWeather);
            hashMap.put("historySevenDaysWeather", lastMonthWeather);
        }
        return hashMap;
    }

    @Override
    public HashMap<String, List<BatteryDivinerVO>> getElectricityByCity(String city) {
        HashMap<String, List<BatteryDivinerVO>> hashMap = new HashMap<>();
        // 获取近14天辐射量
        List<BatteryDivinerVO> historyFutureElectricity = new ArrayList<>();
        List<BatteryDivinerVO> historyRealElectricity = new ArrayList<>();
        List<BatteryDivinerVO> futureFutureElectricity = new ArrayList<>();

        List<List<?>> predictElectricityByCity = forecastMapper.getPredictElectricityByCity(city);
        List<PredictRegionalElectricityVO> predictRegionalElectricityVos = (List<PredictRegionalElectricityVO>) predictElectricityByCity.get(1);
        List<RegionalElectricityVO> regionalElectricityVos = (List<RegionalElectricityVO>) predictElectricityByCity.get(0);
        regionalElectricityVos.sort(Comparator.comparing(RegionalElectricityVO::getCollectDate));
        regionalElectricityVos.forEach(regionalElectricityVO -> {
            String collectDate = regionalElectricityVO.getCollectDate();
            BatteryDivinerVO historyFuture = new BatteryDivinerVO();
            historyFuture.setCollectDate(collectDate);
            historyFuture.setElectricity(regionalElectricityVO.getPredictElectricity());
            historyFutureElectricity.add(historyFuture);
            BatteryDivinerVO historyReal = new BatteryDivinerVO();
            historyReal.setCollectDate(collectDate);
            historyReal.setElectricity(regionalElectricityVO.getElectricity());
            historyRealElectricity.add(historyReal);
        });
        predictRegionalElectricityVos.forEach(predictRegionalElectricityVO -> {
            BatteryDivinerVO batteryDivinerVO = new BatteryDivinerVO();
            batteryDivinerVO.setCollectDate(predictRegionalElectricityVO.getCollectDate());
            batteryDivinerVO.setElectricity(predictRegionalElectricityVO.getFutureElectricity());
            futureFutureElectricity.add(batteryDivinerVO);
        });
        // historyFutureElectricity.remove(historyFutureElectricity.size() - 1);
        // BatteryDivinerVO batteryDivinerVO = futureFutureElectricity.get(0);
        // historyFutureElectricity.add(batteryDivinerVO);
        hashMap.put("historyFutureElectricity", historyFutureElectricity);
        hashMap.put("futureFutureElectricity", futureFutureElectricity);
        hashMap.put("historyRealElectricity", historyRealElectricity);
        return hashMap;
    }

    @Override
    public List<DateRadiationVO> getHistoryRadiationList(List<LocalDate> dates, String city) {
        return meteorologyMapper.getHistoryRadiationList(city, dates);
    }

    @Override
    public IPage<ForecastPlantVO> getForecastPlantList(ForecastPlantDTO query) {
        IPage<ForecastPlantVO> iPage = new Page<>(query.getCurrentPage(), query.getPageSize());
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        return forecastMapper.getForecastPlantList(userInfo, iPage, query);
    }

    public IPage<ForecastPlantVO> getForecastPlantListByCity(ForecastPlantDTO query) {
        IPage<ForecastPlantVO> iPage = new Page<>(query.getCurrentPage(), query.getPageSize());
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        return forecastMapper.getForecastPlantListByCity(userInfo, iPage, query);
    }

    @Override
    public HashMap<String, Object> getGradeEvaluationPlantList(ForecastPlantDTO query) {
        String date = query.getDate();
        if (StrUtil.isEmpty(date) || !DateUtils.checkDate(date)) {
            throw new BusinessException(DATETIME_FORMAT_FAILED);
        }

        Integer currentPage = query.getCurrentPage();
        Integer pageSize = query.getPageSize();
        query.setCurrentPage(-1);
        query.setPageSize(-1);
        List<ForecastPlantVO> records = new ArrayList<>();
        if (DateUtils.getTodayDate().equals(date)) {
            IPage<ForecastPlantVO> forecastPlantList = getForecastPlantList(query);
            records = forecastPlantList.getRecords();
        } else {
            IPage<ForecastPlantVO> forecastPlantListByCity = getForecastPlantListByCity(query);
            records = forecastPlantListByCity.getRecords();
        }
        int recordsSize = records.size();
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("total", recordsSize);
        List<String> predefinedIntervals = Arrays.asList(
                "0~10", "10~20", "20~30", "30~40", "40~50",
                "50~60", "60~70", "70~80", "80~90", "90~100"
        );
        records.forEach(forecastPlant -> {
            String forecastElectricity = forecastPlant.getForecastElectricity();
            if (StrUtil.isNotBlank(forecastElectricity)) {
                Double forecastElectricityValue = Double.parseDouble(forecastElectricity);
                Double todayElectricityValue = Double.parseDouble(forecastPlant.getTodayElectricity());
                double result = todayElectricityValue / forecastElectricityValue;
                if (result > 1.0) {
                    result = 1.0;
                }
                forecastPlant.setPercentage(result);
                String s = result >= 0.8 ? "优秀" : result >= 0.7 ? "良好" : result >= 0.6 ? "合格" : "不合格";
                forecastPlant.setGrade(s);
            } else {
                forecastPlant.setGrade("未知");
                forecastPlant.setPercentage(0.0);
            }
        });
        if (ForecastPlantDTO.SINGLE_DATA_TYPE.equals(query.getDataType())) {
            Map<String, Long> groupedCounts = new LinkedHashMap<>();
            for (String interval : predefinedIntervals) {
                int from = Integer.parseInt(interval.split("~")[0]);
                int to = Integer.parseInt(interval.split("~")[1]);
                groupedCounts.put(interval, records.stream()
                        .filter(plant -> plant.getPercentage() * 100 >= from && plant.getPercentage() * 100 <= to)
                        .count());
            }

            String grade = query.getGrade();
            if (StrUtil.isEmpty(grade)) {
                grade = "0~100";
            }
            int from = Integer.parseInt(grade.split("~")[0]);
            int to = Integer.parseInt(grade.split("~")[1]);

            records = records.stream()
                    .filter(plant -> plant.getPercentage() * 100 >= from && plant.getPercentage() * 100 <= to)
                    .collect(Collectors.toList());
            for (ForecastPlantVO record : records) {
                record.setPercentage(record.getPercentage() * 100);
            }
            if (query.getIsAsc() && "percentage".equals(query.getOrder())) {
                List<ForecastPlantVO> pageData = getGradePlantPageData(records, currentPage, pageSize, true);
                hashMap.put("pageData", pageData);
            } else {
                List<ForecastPlantVO> pageData = getGradePlantPageData(records, currentPage, pageSize, false);
                hashMap.put("pageData", pageData);
            }
            hashMap.put("groupedByPercentages", groupedCounts);
        } else if (ForecastPlantDTO.MULTI_DATA_TYPE.equals(query.getDataType())) {
            for (ForecastPlantVO record : records) {
                record.setPercentage(record.getPercentage() * 100);
            }
            Map<String, List<ForecastPlantVO>> groupedRecords = new LinkedHashMap<>();
            for (String interval : predefinedIntervals) {
                groupedRecords.put(interval, new ArrayList<>());
            }
            for (ForecastPlantVO vo : records) {
                double percentage = (Double.parseDouble(vo.getTodayElectricity()) / Double.parseDouble(vo.getForecastElectricity())) * 100;
                String intervalKey = getIntervalKey(percentage, predefinedIntervals);
                // 将 vo 添加到对应的区间列表中
                groupedRecords.get(intervalKey).add(vo);
            }
            hashMap.put("allGroupedRecords", groupedRecords);
        } else {
            throw new BusinessException(ResultEnum.REQUESTPARAM_ERROR);
        }
        return hashMap;
    }

    private static String getIntervalKey(double percentage, List<String> predefinedIntervals) {
        if (percentage > 100) {
            percentage = 100;
        }
        for (String interval : predefinedIntervals) {
            String[] bounds = interval.split("~");
            int lowerBound = Integer.parseInt(bounds[0]);
            int upperBound = Integer.parseInt(bounds[1]);

            if (percentage >= lowerBound && percentage <= upperBound) {
                return interval;
            }

        }
        return "Unknown Range";
    }

    @Override
    public String getCurrentForecastElectricity(String plantId) {
        return forecastMapper.getCurrentForecastElectricity(plantId);
    }

    @Override
    public HashMap<String, List<PlantPowerVO>> getEvaluateChart(String plantId, String date) {
        HashMap<String, List<PlantPowerVO>> hashMap = new HashMap<>();
        // 预测功率
        List<PlantPowerVO> forecastPower = forecastMapper.getPredictPower(plantId, date);
        hashMap.put("forecastPower", forecastPower);
        // 实际功率
        List<PlantPowerVO> realPower = deviceServiceClient.getTotalPowerListByPlantUid(plantId, date);
        hashMap.put("realPower", realPower);
        return hashMap;
    }

    @Override
    public WeatherDataVO getWeatherData(String plantId) {

        // 根据工作效率排名获取默认 plantId
        if (StrUtil.isBlank(plantId)) {
            List<String> plantNameList = plantMapper.getPlantNameListInWeatherStation("0C");

            PageResult<String> pageList = weatherStationService.getPageList(new WeatherStationQuery());
            List<String> cities = pageList.getList();
            if (CollUtil.isEmpty(cities)) {
                throw new BusinessException("暂无数据");
            }
            PowerPlantInfoQueryDTO query = new PowerPlantInfoQueryDTO();
            query.setCity(cities);
            query.setCurrentPage(1);
            query.setPageSize(1);
            query.setPlantName(plantNameList);
            // 户租才有气象站
            query.setProjectSpecial(ProjectTypeEnum.HOUSEHOLD_LEASE.projectID.toString());
            IPage<WorkEfficiencyVO> page = plantService.getPlantElectricityRank(query);
            List<WorkEfficiencyVO> records = page.getRecords();
            if (CollUtil.isEmpty(records)) {
                throw new BusinessException("暂无数据");
            }
            plantId = records.get(0).getPlantUid();
        }

        Date date = new Date();
        String begin = DateUtils.getDayStart(date);
        String end = DateUtils.getDayEnd(date);
        WeatherDataVO weatherData = new WeatherDataVO();
        HashMap<String, Object> hashMap = verifyPlantId(plantId);
        PlantVO plantInfo = (PlantVO) hashMap.get("plantInfo");
        BigDecimal salePrice = plantInfo.getSalePrice();
        PlantAddressDTO plantAddressDTO = (PlantAddressDTO) hashMap.get("plantAddressDTO");
        List<MeteorologyEntity> meteorologyEntities = getMeteorologyListByPlantId(plantId, begin, end);
        List<WeatherEntity> weatherEntities = weatherMapper.selectList("bto_weather_" + DateUtils.getCurrentMonth(date), begin, end, plantAddressDTO.getCity());
        long currentTime = new Date().getTime();
        // 假设第一条数据为距离当前时间最近的一条天气数据
        WeatherEntity latestWeatherEntity = weatherEntities.get(0);
        long weatherEntityTimeDifference = Math.abs(latestWeatherEntity.getTime().getTime() - currentTime);
        for (WeatherEntity weatherEntity : weatherEntities) {
            Date entityTime = weatherEntity.getTime();
            // 天气预报时间加半小时，大于当前时间，则设为最新的天气预报信息
            // 如当前时间早上9点20分，天气预报为九点整，则取九点的天气预报
            // 如当前时间早上9点35分，天气预报为九点整，添加半小时不大于当前时间，则取十点整的天气预报
            if (entityTime.getTime() + 1800000 > currentTime) {
                break;
            }
            if (Math.abs(entityTime.getTime() - currentTime) < weatherEntityTimeDifference) {
                latestWeatherEntity = weatherEntity;
                weatherEntityTimeDifference = Math.abs(entityTime.getTime() - currentTime);
            }
        }
        // 获取最新一条气象信息
        MeteorologyEntity meteorologyEntity = meteorologyEntities.get(meteorologyEntities.size() - 1);
        ForecastBaseInfoVO convert = ForecastBaseInfoConvert.INSTANCE.convert(meteorologyEntity, latestWeatherEntity);
        weatherData.setForecastBaseInfoVO(convert);

        // 气象基础信息
        ForecastBaseInfoVO forecastPlantData = getPlantBaseInfo(plantInfo, weatherData.getForecastBaseInfoVO());
        forecastPlantData.setWindDirection(WindEnum.getValueByName(forecastPlantData.getWindDirection()));
        weatherData.setForecastBaseInfoVO(forecastPlantData);

        String predictElectricity = getCurrentForecastElectricity(plantId);
        if (StrUtil.isEmpty(predictElectricity)) {
            throw new BusinessException("电站模型尚未完善，发电预测失败");
        }
        BigDecimal futureTodayElectricity = new BigDecimal(predictElectricity);
        ForecastBaseInfoVO forecastBaseInfoVO = weatherData.getForecastBaseInfoVO();
        forecastBaseInfoVO.setForecastTodayElectricity(futureTodayElectricity.toString());

        forecastBaseInfoVO.setForecastTodayEarning(salePrice.multiply(futureTodayElectricity).toString());
        String todayElectricity = plantInfo.getTodayElectricity();
        String plantCapacity = plantInfo.getPlantCapacity();
        BigDecimal capacity = new BigDecimal(plantCapacity);
        forecastBaseInfoVO.setEquivalentHour(BusinessCalculateUtil.getRealEfficiencyPerHours(todayElectricity, plantCapacity));
        forecastBaseInfoVO.setPlantEfficiency(BusinessCalculateUtil.getWorkEfficiency(plantInfo.getPower().toString(), capacity.multiply(new BigDecimal("1000")).toString()));
        // 计算理论辐射量
        forecastBaseInfoVO.setTheoryRadiantQuantity(BusinessCalculateUtil.getRadiation(futureTodayElectricity.toString(), plantCapacity));
        forecastBaseInfoVO.setRadiantQuantity(BusinessCalculateUtil.getRadiation(todayElectricity, plantCapacity));

        return weatherData;
    }

    public HashMap<String, Object> verifyPlantId(String plantId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        PlantVO plantInfo = plantService.getPlantInfo(plantId);
        if (Objects.isNull(plantInfo)) {
            throw new BusinessException("电站ID填写错误或不存在");
        } else {
            String orientation = plantInfo.getOrientation();
            if (Objects.nonNull(orientation)) {
                if (OrientationEnum.divide.getName().equals(orientation)) {
                    plantInfo.setOrientation(OrientationEnum.HERRINGBONE.getName());
                }
                if (OrientationEnum.SAME.getName().equals(orientation)) {
                    plantInfo.setOrientation(OrientationEnum.IN_LINE.getName());
                }
            } else {
                plantInfo.setOrientation(OrientationEnum.NONE.getName());
            }
        }
        PlantAddressDTO plantAddressDTO = PlantAddressConvert.INSTANCE.convert(plantInfo);
        hashMap.put("plantAddressDTO", plantAddressDTO);
        hashMap.put("plantInfo", plantInfo);
        return hashMap;
    }

    public List<ForecastPlantVO> getGradePlantPageData(List<ForecastPlantVO> forecastPlants, int currentPage, int pageSize, Boolean isAsc) {
        if (forecastPlants.isEmpty() || currentPage < 0) {
            return Collections.emptyList();
        }

        // 计算起始索引，注意列表索引从0开始，所以需要减1
        int startIndex = (currentPage - 1) * pageSize;

        // 判断起始索引是否超出总数据范围
        if (startIndex >= forecastPlants.size()) {
            // 如果超出范围，返回空列表
            return Collections.emptyList();
        }

        // 计算结束索引（包含）
        int endIndex = Math.min(startIndex + pageSize, forecastPlants.size());
        if (isAsc) {
            forecastPlants.sort(Comparator.comparing(ForecastPlantVO::getPercentage));
        } else {
            forecastPlants.sort(Comparator.comparing(ForecastPlantVO::getPercentage).reversed());

        }
        return new ArrayList<>(forecastPlants.subList(startIndex, endIndex));
    }


    public Double acquiredRadiation(List<WeatherEntity> weatherEntities, List<WeatherRadiationVO> weatherRadiations) {
        List<WeatherRadiationVO> matchingEntities = new ArrayList<>();
        for (WeatherRadiationVO vo : weatherRadiations) {
            for (WeatherEntity weatherEntity : weatherEntities) {
                String format = DateUtil.format(weatherEntity.getTime(), "HH:mm:ss");
                if (vo.getTime().equals(format) && vo.getWeather().equals(weatherEntity.getWeather())) {
                    matchingEntities.add(vo);
                }
            }
        }
        return matchingEntities.stream().mapToDouble(WeatherRadiationVO::getAvgRadiation).sum();
    }

    @Override
    public ForecastBaseInfoVO getPlantBaseInfo(PlantVO plantInfo, ForecastBaseInfoVO forecastBaseInfoVO) {
        forecastBaseInfoVO.setTodayElectricity(plantInfo.getTodayElectricity());
        forecastBaseInfoVO.setPlantCapacity(plantInfo.getPlantCapacity());
        forecastBaseInfoVO.setPlantStatus(plantInfo.getPlantStatus());
        forecastBaseInfoVO.setTodayEarning(plantInfo.getTodayEarning());
        forecastBaseInfoVO.setPower(plantInfo.getPower());
        forecastBaseInfoVO.setLatitude(plantInfo.getLatitude());
        forecastBaseInfoVO.setLongitude(plantInfo.getLongitude());
        PlantInfoEntity plantInfoEntity = plantInfoService.getById(plantInfo.getPlantUid());
        forecastBaseInfoVO.setPlantReplenishInfo(PlantInfoConvert.INSTANCE.convert(plantInfoEntity));
        forecastBaseInfoVO.setOrientation(plantInfo.getOrientation());
        return forecastBaseInfoVO;
    }

    @Override
    public AbstractMap<String, String> predictionElectByProject(String projectId, int size) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        String today = DateUtil.today();
        // 构造Redis中存储未来15天预测发电量的key
        String redisKey = PREDICTION_ELECTRICITY + userInfo.getUserUid() + projectId + REDIS_SPLIT + today + REDIS_SPLIT + size;

        // 尝试从Redis中获取预测发电量
        Object obj = redisUtil.get(redisKey);
        // 如果存在，直接返回
        if (obj != null) {
            return (AbstractMap<String, String>) obj;
        }

        PlantEvaluationQuery query = new PlantEvaluationQuery();
        query.setProjectId(projectId);

        List<String> plantCities = getCity(userInfo, query);

        if (CollUtil.isEmpty(plantCities)) {
            return new HashMap<>();
        }
        List<DateRadiationVO> futureRadiationList = getFutureRadiationList(plantCities, size);
        TreeMap<String, String> map = new TreeMap<>();
        for (DateRadiationVO item : futureRadiationList) {
            String predictionElectricity = radiantQuantityService.getPredictionElectricityByRadiation(item.getRadiation(), projectId);
            map.put(item.getDate(), BusinessCalculateUtil.getRealElectricity(predictionElectricity));
        }

        redisUtil.set(redisKey, map, DateUtils.getExpTimeByEndOfToday());
        return map;
    }

    @NotNull
    private List<DateRadiationVO> getFutureRadiationList(List<String> plantCities, int size) {
        // 获取未来15天天气情况
        List<WeatherFutureVO> futureWeatherList = getFutureSevenDayWeatherList(plantCities, size);
        List<DateRadiationVO> futureRadiationList = new ArrayList<>();
        for (WeatherFutureVO weatherFutureVO : futureWeatherList) {
            // 获取天气字段
            String weather = weatherFutureVO.getWeather();
            Double average = weatherRadiationService.getRadiationByWeather(weather);
            if (average == null) {
                average = 0.0;
            }
            DateRadiationVO dateRadiationVO = new DateRadiationVO(new SimpleDateFormat(DateUtils.DATE_PATTERN).format(weatherFutureVO.getCollectDate()), Optional.ofNullable(average).orElse(0.0).toString());
            futureRadiationList.add(dateRadiationVO);
        }
        return futureRadiationList;
    }

    @Override
    public Map<String, Integer> predictionElectAnalysis(String projectId) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        String today = DateUtil.today();
        String redisKey = ELECTRICITY_BENEFIT + userInfo.getUserUid() + projectId + REDIS_SPLIT + today;

        Object obj = redisUtil.get(redisKey);
        if (obj != null) {
            return (Map<String, Integer>) obj;
        }


        PlantEvaluationQuery query = new PlantEvaluationQuery();
        query.setProjectId(projectId);

        // 获取项目下存在气象站的city
        List<String> plantCities = getCity(userInfo, query);

        if (CollUtil.isEmpty(plantCities)) {
            return Collections.emptyMap();
        }

        List<PlantVO> plantIdWithCity = plantMapper.getPlantIdWithCity(userInfo, projectId);
        HashMap<String, BigDecimal> cityWithRadiationMap = new HashMap<>();


        for (String city : plantCities) {
            BigDecimal radiation = cityWithRadiationMap.getOrDefault(city, BigDecimal.valueOf(0));

            List<DateRadiationVO> futureRadiationList = getFutureRadiationList(Collections.singletonList(city), 15);
            for (DateRadiationVO dateRadiationVO : futureRadiationList) {
                BigDecimal bigDecimal = new BigDecimal(dateRadiationVO.getRadiation());
                radiation = radiation.add(bigDecimal);
            }
            cityWithRadiationMap.put(city, radiation);
        }

        ArrayList<BigDecimal> benefitList = new ArrayList<>();
        for (PlantVO plantVO : plantIdWithCity) {
            BigDecimal cityRadiation = cityWithRadiationMap.getOrDefault(plantVO.getCity(),BigDecimal.ZERO);
            //获取未来发电量
            String futureForecastData = forecastMapper.getFutureForecastData(plantVO.getPlantUid(), cityRadiation.doubleValue());

            BigDecimal predictElectricity = new BigDecimal(BusinessCalculateUtil.getRealElectricity(Optional.ofNullable(futureForecastData).orElse("0")));
            benefitList.add(predictElectricity.multiply(ELECTRICITY_BENEFIT_MULTIPLIER).setScale(2, RoundingMode.HALF_UP));
        }

        if (CollUtil.isEmpty(benefitList)) {
            return Collections.emptyMap();
        }
        Collections.sort(benefitList, Comparator.naturalOrder());
        Map<String, BigDecimal[]> complianceRanges = RangeUtils.generateComplianceRanges(5, benefitList.get(benefitList.size() - 1).toString());

        Map<String, Integer> countMap = RangeUtils.getCountMap(benefitList, null, complianceRanges);

        redisUtil.set(redisKey, countMap, DateUtils.getExpTimeByEndOfToday());
        return countMap;
    }


    // 获取项目下存在气象站的city
    @NotNull
    public List<String> getCity(RequireParamsDTO userInfo, PlantEvaluationQuery query) {
        // 获取存在气象站的city
        WeatherStationQuery weatherStationQuery = new WeatherStationQuery();
        weatherStationQuery.setCurrentPage(-1);
        weatherStationQuery.setPageSize(-1);
        PageResult<String> pageList = weatherStationService.getPageList(weatherStationQuery);
        List<String> weatherStationCityList = pageList.getList();

        // 获取项目下的所有city
        List<String> plantCities = plantMapper.getPlantCities(userInfo, query);

        // 交集
        plantCities.retainAll(weatherStationCityList);
        return plantCities;
    }

    @Override
    public HashMap<String, BigDecimal> predictionElectricity(String projectId) {

        HashMap<String, BigDecimal> map = new HashMap<>();
        AbstractMap<String, String> oneDayMap = this.predictionElectByProject(projectId, 1);

        AbstractMap<String, String> fifteenDayMap = this.predictionElectByProject(projectId, 15);
        AbstractMap<String, String> thirtyDayMap = this.predictionElectByProject(projectId, 30);

        BigDecimal one = getBenefit(oneDayMap);
        map.put("one", one);

        BigDecimal fifteen = getBenefit(fifteenDayMap);
        map.put("fifteen", fifteen);

        BigDecimal thirty = getBenefit(thirtyDayMap);
        map.put("thirty", thirty);
        return map;
    }

    @NotNull
    private static BigDecimal getBenefit(AbstractMap<String, String> oneDayMap) {
        BigDecimal result = BigDecimal.ZERO;
        for (Map.Entry<String, String> entry : oneDayMap.entrySet()) {
            String electricity = entry.getValue();
            if (electricity != null) {
                BigDecimal bigDecimal = new BigDecimal(electricity);
                result = result.add(bigDecimal);
            }
        }
        // 设置结果保留两位小数
        return result.multiply(ELECTRICITY_BENEFIT_MULTIPLIER).setScale(2, BigDecimal.ROUND_HALF_UP);
    }
}
