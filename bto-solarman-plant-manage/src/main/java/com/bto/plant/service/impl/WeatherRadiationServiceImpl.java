package com.bto.plant.service.impl;

import com.bto.commons.pojo.entity.WeatherRadiationEntity;
import com.bto.commons.pojo.vo.DateRadiationVO;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.commons.utils.DateUtils;
import com.bto.plant.dao.WeatherRadiationMapper;
import com.bto.plant.service.WeatherRadiationService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 一天中天气与辐射量关系表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-01-06
 */
@Service
@AllArgsConstructor
public class WeatherRadiationServiceImpl extends BaseServiceImpl<WeatherRadiationMapper, WeatherRadiationEntity> implements WeatherRadiationService {

    private final WeatherRadiationMapper weatherRadiationMapper;

    @Override
    public Double getRadiationByWeather(String weather) {
        return weatherRadiationMapper.getRadiationByWeather(weather);
    }

    @Override
    public DateRadiationVO getHistoryRadiationByWeather(LocalDate localDate, String city) {
        String tableName = "bto_weather_" + DateUtils.getCurrentMonth(localDate);
        return weatherRadiationMapper.getHistoryRadiationByWeather(tableName, localDate, city);
    }
}