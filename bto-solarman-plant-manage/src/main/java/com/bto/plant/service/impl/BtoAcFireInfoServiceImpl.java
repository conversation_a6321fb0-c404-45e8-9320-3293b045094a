package com.bto.plant.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bto.commons.pojo.dto.BatteryQuery;
import com.bto.commons.pojo.entity.BtoAcFireInfoEntity;
import com.bto.commons.pojo.vo.BtoAcFireInfoVO;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.plant.convert.BtoAcFireInfoConvert;
import lombok.AllArgsConstructor;
import com.bto.plant.dao.BtoAcFireInfoDao;
import com.bto.plant.service.BtoAcFireInfoService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 空调消防信息(实时)
 *
 * <AUTHOR>
 * @since 1.0.0 2025-04-22
 */
@Service
@AllArgsConstructor
public class BtoAcFireInfoServiceImpl extends BaseServiceImpl<BtoAcFireInfoDao, BtoAcFireInfoEntity> implements BtoAcFireInfoService {

    @Override
    public List<BtoAcFireInfoVO> getBatteryGuardInfo(BatteryQuery query) {
        Date date = new Date();
        List<String> inverterSn = query.getInverterSn();
        if (inverterSn != null && inverterSn.size() == 1) {
            LambdaQueryWrapper<BtoAcFireInfoEntity> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(BtoAcFireInfoEntity::getInverterSn, inverterSn.get(0))
                    .ge(BtoAcFireInfoEntity::getInitTime, DateUtil.beginOfDay(date))
                    .lt(BtoAcFireInfoEntity::getInitTime, DateUtil.endOfDay(date));
            List<BtoAcFireInfoEntity> btoAcFireInfoEntities = baseMapper.selectList(wrapper);
            return BtoAcFireInfoConvert.INSTANCE.convertList(btoAcFireInfoEntities);
        } else {
            // 工商业/多逆变器
        }
        return null;
    }
}