package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.commons.constant.DeviceType;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.handler.MapResultHandler;
import com.bto.commons.pojo.dto.FunctionalInstrumentQuery;
import com.bto.commons.pojo.entity.FunctionalInstrumentEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.dao.CounterDao;
import com.bto.plant.dao.FunctionalInstrumentDao;
import com.bto.plant.service.FunctionalInstrumentService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 多功能仪表数据
 *
 * <AUTHOR>
 * @since 2024-10-09
 */
@Service
@AllArgsConstructor
@Slf4j
public class FunctionalInstrumentServiceImpl extends BaseServiceImpl<FunctionalInstrumentDao, FunctionalInstrumentEntity> implements FunctionalInstrumentService {

    public final GlobalParamUtil globalParamUtil;
    public final DeviceServiceClient deviceServiceClient;
    public final CounterDao counterDao;

    @Override
    public PageResult<FunctionalInstrumentEntity> page(FunctionalInstrumentQuery query) {
        Result<List<String>> result = deviceServiceClient.getListByType(query.getPlantUid(), DeviceType.FUNCTIONAL_INSTRUMENT.getCode());
        if (result.getStatus().equals(ResultEnum.SUCCESS.getCode())) {
            List<String> ids = result.getData(new TypeReference<List<String>>() {
            });
            if (CollUtil.isEmpty(ids)) {
                throw new BusinessException("电站不存在多功能仪表");
            }
            query.setDeviceId(ids);
        }

        Page<FunctionalInstrumentEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        page = baseMapper.page(page, query, null);
        List<FunctionalInstrumentEntity> records = page.getRecords();
        List<String> functionIds = records.stream().map(FunctionalInstrumentEntity::getDeviceId).collect(Collectors.toList());
        MapResultHandler<String, String> map= new MapResultHandler<>();
        counterDao.getMap(functionIds,map);

        records.forEach(item->{
            item.setInverterId(map.get(item.getDeviceId()));
        });
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    private LambdaQueryWrapper<FunctionalInstrumentEntity> getWrapper(FunctionalInstrumentQuery query) {
        LambdaQueryWrapper<FunctionalInstrumentEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }


}