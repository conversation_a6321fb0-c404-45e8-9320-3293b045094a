package com.bto.plant.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bto.commons.pojo.dto.BtoDeviceHomeQuery;
import com.bto.commons.pojo.entity.BtoDeviceHomeEntity;
import com.bto.commons.pojo.vo.BtoDeviceHomeVO;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.oauth.global.GlobalParamUtil;
import lombok.AllArgsConstructor;
import com.bto.plant.convert.BtoDeviceHomeConvert;
import com.bto.plant.dao.BtoDeviceHomeDao;
import com.bto.plant.service.BtoDeviceHomeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 家居设备
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-26
 */
@Service
@AllArgsConstructor
public class BtoDeviceHomeServiceImpl extends BaseServiceImpl<BtoDeviceHomeDao, BtoDeviceHomeEntity> implements BtoDeviceHomeService {

    private final GlobalParamUtil globalParamUtil;

    @Override
    public List<BtoDeviceHomeVO> getList(BtoDeviceHomeQuery query) {
        globalParamUtil.checkUserPermission(query.getUserUid());
        List<BtoDeviceHomeEntity> list = baseMapper.selectList(getWrapper(query));
        return BtoDeviceHomeConvert.INSTANCE.convertList(list);
    }

    private LambdaQueryWrapper<BtoDeviceHomeEntity> getWrapper(BtoDeviceHomeQuery query) {
        LambdaQueryWrapper<BtoDeviceHomeEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BtoDeviceHomeEntity::getUserUid, query.getUserUid());
        wrapper.eq(Objects.nonNull(query.getDeviceAddr()), BtoDeviceHomeEntity::getDeviceAddr, query.getDeviceAddr());
        wrapper.orderByDesc(BtoDeviceHomeEntity::getOrder);
        wrapper.orderByDesc(BtoDeviceHomeEntity::getUpdateTime);
        return wrapper;
    }

    @Override
    public void handleSaveOrUpdate(BtoDeviceHomeVO vo) {
        globalParamUtil.checkUserPermission(vo.getUserUid());
        BtoDeviceHomeEntity entity = BtoDeviceHomeConvert.INSTANCE.convert(vo);
        if (entity.getDeviceId() == null) {
            // todo 设置设备id
            baseMapper.insert(entity);
        } else {
            updateById(entity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(BtoDeviceHomeQuery query) {
        globalParamUtil.checkUserPermission(query.getUserUid());
        removeByIds(query.getDeviceIds());
    }

    @Override
    public BtoDeviceHomeVO getDeviceInfo(String deviceId, String userUid) {
        globalParamUtil.checkUserPermission(userUid);
        LambdaQueryWrapper<BtoDeviceHomeEntity> queryWrapper = Wrappers.lambdaQuery(BtoDeviceHomeEntity.class);
        queryWrapper.eq(BtoDeviceHomeEntity::getDeviceId, deviceId);
        queryWrapper.eq(BtoDeviceHomeEntity::getUserUid, userUid);
        BtoDeviceHomeEntity btoDeviceHomeEntity = baseMapper.selectOne(queryWrapper);
        if (btoDeviceHomeEntity != null) {
            return BtoDeviceHomeConvert.INSTANCE.convert(btoDeviceHomeEntity);
        }
        return null;
    }

}