package com.bto.plant.convert;

import com.bto.commons.pojo.dto.PlantAddressDTO;
import com.bto.commons.pojo.dto.PlantVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> by zhb on 2023/12/25.
 */
@Mapper
public interface PlantAddressConvert {

    PlantAddressConvert INSTANCE = Mappers.getMapper(PlantAddressConvert.class);

    PlantAddressDTO convert(PlantVO vo);

}
