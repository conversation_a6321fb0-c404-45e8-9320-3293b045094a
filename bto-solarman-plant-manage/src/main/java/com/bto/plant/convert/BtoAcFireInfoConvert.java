package com.bto.plant.convert;

import com.bto.commons.pojo.entity.BtoAcFireInfoEntity;
import com.bto.commons.pojo.vo.BtoAcFireInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 空调消防信息转换器
 * 
 * 提供BtoAcFireInfoEntity与BtoAcFireInfoVO之间的转换功能，使用MapStruct进行对象映射
 * 支持实时空调消防信息数据的实体与视图对象转换
 * 
 * <AUTHOR> 
 * @since 1.0.0 2025-04-22
 */
@Mapper
public interface BtoAcFireInfoConvert {
    BtoAcFireInfoConvert INSTANCE = Mappers.getMapper(BtoAcFireInfoConvert.class);

    /**
     * VO转Entity
     * 
     * 将BtoAcFireInfoVO视图对象转换为BtoAcFireInfoEntity实体对象
     * 
     * @param vo 视图对象
     * @return 实体对象
     */
    BtoAcFireInfoEntity convert(BtoAcFireInfoVO vo);

    /**
     * Entity转VO
     * 
     * 将BtoAcFireInfoEntity实体对象转换为BtoAcFireInfoVO视图对象
     * 
     * @param entity 实体对象
     * @return 视图对象
     */
    BtoAcFireInfoVO convert(BtoAcFireInfoEntity entity);

    /**
     * Entity列表转VO列表
     * 
     * 将BtoAcFireInfoEntity实体对象列表转换为BtoAcFireInfoVO视图对象列表
     * 
     * @param list 实体对象列表
     * @return 视图对象列表
     */
    List<BtoAcFireInfoVO> convertList(List<BtoAcFireInfoEntity> list);

}