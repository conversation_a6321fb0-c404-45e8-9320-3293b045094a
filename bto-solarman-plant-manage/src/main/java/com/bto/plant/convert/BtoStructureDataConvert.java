package com.bto.plant.convert;

import com.bto.commons.pojo.entity.BtoStructureDataEntity;
import com.bto.commons.pojo.vo.BtoStructureDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 结构件监控数据
*
* <AUTHOR> 
* @since  2025-06-18
*/
@Mapper
public interface BtoStructureDataConvert {
    BtoStructureDataConvert INSTANCE = Mappers.getMapper(BtoStructureDataConvert.class);

    BtoStructureDataEntity convert(BtoStructureDataVO vo);

    BtoStructureDataVO convert(BtoStructureDataEntity entity);

    List<BtoStructureDataVO> convertList(List<BtoStructureDataEntity> list);

}