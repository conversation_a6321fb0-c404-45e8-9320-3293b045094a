package com.bto.plant.convert;

import com.bto.commons.pojo.entity.PlantInfoEntity;
import com.bto.commons.pojo.vo.BtoPlantInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电站信息（补充）
 *
 * <AUTHOR>
 * @since 1.0.0 2024-01-04
 */
@Mapper
public interface PlantInfoConvert {
    PlantInfoConvert INSTANCE = Mappers.getMapper(PlantInfoConvert.class);

    PlantInfoEntity convert(BtoPlantInfoVO vo);

    BtoPlantInfoVO convert(PlantInfoEntity entity);

    List<BtoPlantInfoVO> convertList(List<PlantInfoEntity> list);

}