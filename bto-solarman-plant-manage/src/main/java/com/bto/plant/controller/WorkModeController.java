package com.bto.plant.controller;

import com.alibaba.fastjson.JSONObject;
import com.bto.commons.pojo.dto.PassiveModeDTO;
import com.bto.commons.pojo.dto.PassivePowerDTO;
import com.bto.commons.pojo.dto.SOCRetainDTO;
import com.bto.commons.pojo.dto.WorkModeConfigDTO;
import com.bto.commons.pojo.entity.BtoSetParameter;
import com.bto.commons.response.Result;
import com.bto.plant.service.WorkModeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> by zhb on 2025/4/15.
 */
@RestController
@RequestMapping("work")
@Api(tags = "储能工作模式")
@AllArgsConstructor
@Slf4j
public class WorkModeController {

    private final WorkModeService workModeService;

    @GetMapping("getWorkModeParam")
    @ApiOperation("查询工作模式控制参数")
    public Result<BtoSetParameter> getWorkModeParam(@RequestParam("deviceSn") String deviceSn) {
        BtoSetParameter result = workModeService.getWorkModeParam(deviceSn);
        return Result.success(result);
    }

    @PostMapping("workModeTimeSet")
    @ApiOperation("设置工作模式及充放电计划")
    public Result<JSONObject> workModeTimeSet(@RequestBody WorkModeConfigDTO workModeConfigDTO) {
        JSONObject result = workModeService.workModeTimeSet(workModeConfigDTO);
        return Result.success(result);
    }

    @PostMapping("backModeSOCSet")
    @ApiOperation("设置后备电池soc保留值")
    public Result<JSONObject> backModeSOCSet(@RequestBody SOCRetainDTO socRetainDTO) {
        JSONObject result = workModeService.backModeSOCSet(socRetainDTO);
        return Result.success(result);
    }

    @PostMapping("passiveModeSet")
    @ApiOperation("设置被动充放电使能")
    public Result<JSONObject> passiveModeSet(@RequestBody PassiveModeDTO passiveModeDTO) {
        JSONObject result = workModeService.passiveModeSet(passiveModeDTO);
        return Result.success(result);
    }

    @PostMapping("passivePowerSet")
    @ApiOperation("设置被动充放电功率")
    public Result<JSONObject> passivePowerSet(@RequestBody PassivePowerDTO passivePowerDTO) {
        JSONObject result = workModeService.passivePowerSet(passivePowerDTO);
        return Result.success(result);
    }


}
