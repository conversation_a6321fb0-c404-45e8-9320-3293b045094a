package com.bto.system.converter;

import com.bto.commons.pojo.entity.DutyPersonEntity;
import com.bto.commons.pojo.vo.DutyPersonVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 运维区域负责名单
*
* <AUTHOR> 
* @since  2024-08-10
*/
@Mapper
public interface DutyPersonConvert {
    DutyPersonConvert INSTANCE = Mappers.getMapper(DutyPersonConvert.class);

    DutyPersonEntity convert(DutyPersonVO vo);

    DutyPersonVO convert(DutyPersonEntity entity);

    List<DutyPersonVO> convertList(List<DutyPersonEntity> list);
    List<DutyPersonEntity> convertEntityList(List<DutyPersonVO> list);

}