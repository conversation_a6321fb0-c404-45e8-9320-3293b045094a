package com.bto.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.entity.User;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.vo.UserInfoVO;
import com.bto.commons.response.Result;
import com.bto.oauth.entity.UserLogin;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户服务接口
 * 定义用户相关的业务操作，包括用户的增删改查、密码管理、角色分配、导入导出等功能
 * <AUTHOR>
 * @date 2023/4/20 14:43
 */
public interface UserService extends IService<User> {
    /**
     * 根据用户ID获取用户名和电话号码
     * 通过用户唯一标识符查询用户的基本联系信息
     * @param userUid 用户唯一标识符
     * @return 包含用户名和电话号码的映射集合
     */
    public Map<String, String> getUserNameAndPhoneByUserUid(String userUid);

    /**
     * 用户列表多条件分页查询
     * 根据查询条件和用户权限分页获取用户信息列表
     * @param query 用户查询条件对象，包含分页参数、用户名、状态等筛选条件
     * @param userInfo 用户请求参数对象，包含当前用户身份和权限信息
     * @return 分页后的用户信息视图对象列表
     */
    Page<UserInfoVO> getUserList(UserQueryDTO query, RequireParamsDTO userInfo);

    /**
     * 新增用户
     * 创建新的用户信息并保存到数据库
     * @param userInfo 用户信息数据传输对象，包含用户名、密码、联系方式等基本信息
     * @return 新增成功返回1，失败返回0
     */
    Integer addUser(UserInfoDTO userInfo);

    /**
     * 修改用户
     * 更新现有用户的基本信息
     * @param userInfo 用户信息数据传输对象，包含用户ID和需要更新的用户信息
     * @return 修改成功返回1，失败返回0
     */
    Integer editUser(UserInfoDTO userInfo);

    /**
     * 删除用户
     * 根据用户ID删除指定的用户信息
     * @param userUid 用户唯一标识符
     * @return 删除成功返回1，失败返回0
     */
    Integer deleteUser(String userUid);

    /**
     * 查询用户信息
     * 根据用户登录信息查询用户的详细信息
     * @param userLogin 用户登录对象，包含登录凭证信息
     * @return 用户信息数据传输对象
     */
    UserDTO selectUserInfo(UserLogin userLogin);

    /**
     * 根据用户Uid查询所属所有电站
     * 获取指定用户所关联的所有电站唯一标识符
     * @param userUid 用户唯一标识符
     * @return 电站UID字符串列表
     */
    List<String> getPlantUidList(String userUid);

    /**
     * 用户密码修改
     * 修改用户的登录密码，需要验证原密码
     * @param userPasswordDTO 密码修改数据传输对象，包含原密码和新密码
     * @param userInfo 用户请求参数对象，包含当前用户身份信息
     * @return 修改成功返回1，失败返回0
     */
    Integer updatePassword(UserPasswordDTO userPasswordDTO, RequireParamsDTO userInfo);

    /**
     * 根据用户id查询用户信息
     * 通过用户唯一标识符获取用户的详细信息
     * @param userUid 用户唯一标识符
     * @return 用户信息视图对象
     */
    UserInfoVO getUserInfoByUserUid(String userUid);

    /**
     * Excel导入用户
     * 通过Excel文件批量导入用户信息
     * @param file Excel文件对象，包含批量用户数据
     * @param password 默认密码，为导入用户统一设置
     * @return 统一响应结果，包含导入成功或失败的信息
     */
    Result importByExcel(MultipartFile file, String password);

    /**
     * 导出用户数据
     * 根据查询条件导出用户信息到Excel文件
     * @param query 用户查询条件对象
     * @param response HTTP响应对象，用于输出Excel文件流
     */
    void export(UserQueryDTO query, HttpServletResponse response);

    /**
     * 角色分配用户
     * 为用户分配系统角色，建立用户与角色的关联关系
     * @param roleUserDTO 角色用户数据传输对象，包含角色ID和用户ID集合
     */
    void assignRoles(RoleUserDTO roleUserDTO);

    /**
     * 通过电话检查用户是否存在
     * 根据手机号码查询用户是否已注册
     * @param phoneNumber 电话号码
     * @return 包含检查结果和提示信息的映射集合
     * <AUTHOR>
     * @since 2024-01-08 14:15:23
     */
    HashMap<Boolean, String> checkUserExistByPhone(String phoneNumber);

    /**
     * 通过验证码重置密码
     * 使用手机验证码重置用户密码
     * @param phoneNumber 用户手机号码
     * @param code 手机验证码
     * @param password 新密码
     * @return 统一响应结果，包含重置成功或失败的信息
     */
    Result resetPasswdByCode(String phoneNumber, String code, String password);

    /**
     * 获取用户布局配置
     * 获取指定用户的个性化界面布局配置
     * @param userId 用户唯一标识符
     * @return 布局配置JSON字符串
     */
    String getLayoutByUser(String userId);

    /**
     * 更新用户布局配置
     * 保存用户的个性化界面布局配置
     * @param userId 用户唯一标识符
     * @param layout 布局配置JSON字符串
     * @return 统一响应结果，包含更新成功或失败的信息
     */
    Result updateLayoutByUser(String userId, String layout);

    /**
     * 获取用户名称和ID映射
     * 批量获取指定用户ID对应的用户名称
     * @param userIds 用户ID列表
     * @return 用户名称和ID的映射集合
     */
    Map<String, String> getNameAndId(List<String> userIds);

    /**
     * 获取用户额外电站信息
     * 获取指定用户的额外电站关联信息
     * @param userUid 用户唯一标识符
     * @return 额外电站信息的映射集合
     */
    Map<String, String> getExtraPlant(String userUid);

    /**
     * 更新用户电站关联
     * 更新用户与电站的关联关系，先删除原有关系再建立新关系
     * @param userUid 用户唯一标识符
     * @param plantIds 电站ID集合
     */
    void updateUserHasPlant(String userUid, Set<String> plantIds);

    /**
     * 添加用户电站关联
     * 为用户添加电站关联关系，不删除原有关系
     * @param userUid 用户唯一标识符
     * @param plantIds 电站ID集合
     */
    void addUserHasPlant(@Param("userUid") String userUid, @Param("plantIds") Set<String> plantIds);

    /**
     * 根据合同ID获取用户信息
     * 通过合同唯一标识符获取关联的用户信息
     * @param contractId 合同唯一标识符
     * @return 用户信息视图对象
     */
    UserInfoVO getUserByContractId(String contractId);
}
