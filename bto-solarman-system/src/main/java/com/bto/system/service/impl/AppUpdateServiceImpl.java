package com.bto.system.service.impl;

import com.bto.system.service.AppUpdateService;
import net.dongliu.apk.parser.ApkFile;
import net.dongliu.apk.parser.bean.ApkMeta;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * APP更新服务实现类
 * 提供APP版本信息查询功能
 * <AUTHOR>
 * @date 2023/5/20 16:39
 */
@Service
public class AppUpdateServiceImpl implements AppUpdateService {

    /**
     * 获取版本信息
     * @return APK版本信息对象，包含版本名称、版本号等信息
     */
    @Override
    public Object versionInfo() {
        String versionInfo = "";
//        File file = new File("/etc/nginx/conf.d/gf/APP/guangyunBto.apk");
        File file = new File("/etc/nginx/conf.d/gf/APP/guangyunBto.apk");
        System.getProperty("file.encoding");
        if (file.exists() && file.isFile()) {
            try {
                ApkFile apkFile = new ApkFile(file);
                ApkMeta apkMeta = apkFile.getApkMeta();
                versionInfo = apkMeta.getVersionName();
                return apkMeta;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return versionInfo;
    }

}
