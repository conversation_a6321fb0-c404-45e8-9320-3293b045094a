package com.bto.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.BtoVersionDTO;
import com.bto.commons.pojo.entity.BtoVersion;
import com.bto.commons.pojo.dto.VersionQueryDTO;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.SorterUtils;
import com.bto.system.dao.VersionMapper;
import com.bto.system.service.VersionService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 版本信息服务实现类
 * 提供版本信息的增删改查功能
 * <AUTHOR>
 * @since 1.0.0 2023-11-20
 */
@Service
@AllArgsConstructor
public class VersionServiceImpl extends ServiceImpl<VersionMapper, BtoVersion> implements VersionService {

    /**
     * 分页查询版本信息
     * @param query 查询条件，包含分页参数
     * @return 版本信息分页结果
     */
    @Override
    public Page<BtoVersion> page(VersionQueryDTO query) {
        Page<BtoVersion> page = SorterUtils.getPage(query);
        return this.page(page);
    }

    /**
     * 获取版本信息查询条件
     * @return 版本信息查询条件
     */
    private static LambdaQueryWrapper<BtoVersion> getWrapper() {
        LambdaQueryWrapper<BtoVersion> queryWrapper = new LambdaQueryWrapper<>();
        return queryWrapper;
    }


    /**
     * 根据ID删除版本信息
     * @param id 版本信息ID
     * @throws BusinessException 删除失败时抛出异常
     */
    @Override
    public void deleteById(Long id) {
        boolean success = this.removeById(id);
        if (!success) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage());
        }
    }

    /**
     * 保存版本信息
     * @param dto 版本信息DTO对象
     * @throws BusinessException 当版本号已存在或保存失败时抛出异常
     */
    @Override
    public void save(BtoVersionDTO dto) {
        BtoVersion entity = BeanUtil.copyProperties(dto, BtoVersion.class);
        LambdaQueryWrapper<BtoVersion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BtoVersion::getVersionNumber, entity.getVersionNumber());
        int count = (int) this.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException(ResultEnum.VERSION_NUMBER_EXISTED.getCode(), ResultEnum.VERSION_NUMBER_EXISTED.getMessage());
        }
        boolean success = this.save(entity);
        if (!success) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage());
        }
    }

    /**
     * 更新版本信息
     * @param dto 版本信息DTO对象
     * @throws BusinessException 当版本号已存在或更新失败时抛出异常
     */
    @Override
    public void update(BtoVersionDTO dto) {
        BtoVersion entity = BeanUtil.copyProperties(dto, BtoVersion.class);
        LambdaQueryWrapper<BtoVersion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(BtoVersion::getId, entity.getId()).eq(BtoVersion::getVersionNumber, entity.getVersionNumber());
        int count = (int) this.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException(ResultEnum.VERSION_NUMBER_EXISTED.getCode(), ResultEnum.VERSION_NUMBER_EXISTED.getMessage());
        }
        boolean success = this.updateById(entity);
        if (!success) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage());
        }
    }
}