package com.bto.system.service;

import com.bto.commons.pojo.dto.WorkOrderDTO;
import com.bto.commons.pojo.vo.ProjectConversionVO;
import com.bto.commons.response.Result;

/**
 * 工单服务接口
 * 提供工单申请、处理、查询等工单相关的业务操作
 * <AUTHOR>
 * @since 2023/12/19 10:32
 */
public interface WorkOrderService {
    /**
     * 工单申请
     * 处理用户提交的工单申请，创建新的工单记录
     * @param dto 工单数据传输对象，包含工单类型、问题描述、项目信息等申请数据
     * @return 统一响应结果，包含申请成功或失败的信息
     */
    Result workOrderApply(WorkOrderDTO dto);

    /**
     * 根据项目专项获取组织ID和维修用户
     * 获取指定项目专项对应的组织机构和可分配的维修用户信息
     * @param projectSpecial 项目专项标识
     * @return 项目转换视图对象，包含组织ID和维修用户列表信息
     */
    ProjectConversionVO getOrgIdWithRepairUserByProjectSpecial(Long projectSpecial);
}
