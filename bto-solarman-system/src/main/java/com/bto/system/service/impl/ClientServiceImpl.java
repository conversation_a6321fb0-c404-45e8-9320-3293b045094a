package com.bto.system.service.impl;

import com.bto.system.dao.ClientMapper;
import com.bto.system.service.ClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户端服务实现类
 * 提供客户端相关查询功能
 * <AUTHOR>
 * @date 2023/8/23 9:49
 */
@Service
public class ClientServiceImpl implements ClientService {
    @Autowired
    private ClientMapper clientMapper;

    /**
     * 获取客户端ID列表
     * @return 客户端ID字符串列表
     */
    @Override
    public List<String> getClientIds() {
        return clientMapper.getClientIds();
    }
}
