package com.bto.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.enums.DictSourceEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.SysDictTypeQueryDTO;
import com.bto.commons.pojo.entity.SysDictDataEntity;
import com.bto.commons.pojo.entity.SysDictTypeEntity;
import com.bto.commons.pojo.vo.DictData;
import com.bto.commons.pojo.vo.SysDictVO;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.system.dao.DictDataMapper;
import com.bto.system.dao.SysDictTypeMapper;
import com.bto.system.service.SysDictDataService;
import com.bto.system.service.SysDictTypeService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统字典类型服务实现类
 * 提供字典类型的增删改查及字典数据管理功能
 * <AUTHOR>
 * @date 2023/10/19 16:07
 */
@Service
@AllArgsConstructor
public class SysDictTypeServiceImpl extends BaseServiceImpl<SysDictTypeMapper, SysDictTypeEntity> implements SysDictTypeService {
    private final DictDataMapper dictDataMapper;
    private final SysDictDataService dictDataService;

    /**
     * 分页查询字典类型
     * @param query 查询条件，包含分页参数、字典类型和字典名称
     * @return 字典类型分页结果
     */
    @Override
    public Page<SysDictTypeEntity> selectDictTypeByPage(SysDictTypeQueryDTO query) {
        Page<SysDictTypeEntity> page = new Page<SysDictTypeEntity>(query.getCurrentPage(), query.getPageSize());
        LambdaQueryWrapper<SysDictTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(SysDictTypeEntity::getDictType, query.getDictType());
        queryWrapper.like(SysDictTypeEntity::getDictName, query.getDictName());
        return baseMapper.selectPage(page, queryWrapper);
    }

    /**
     * 获取所有字典类型列表
     * @return 字典类型实体列表
     */
    @Override
    public List<SysDictTypeEntity> getAllDictTypeList() {
        return baseMapper.selectList(new LambdaQueryWrapper<>());
    }

    /**
     * 根据字典类型ID获取动态SQL数据
     * @param id 字典类型ID
     * @return 动态SQL查询结果列表
     * @throws BusinessException 当SQL执行错误时抛出异常
     */
    @Override
    public List<DictData> getDynamicSql(String id) {
        SysDictTypeEntity entity = this.getById(id);
        try {
           return dictDataMapper.getListForSql(entity.getDictSql());
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.DYNAMIC_SQL_ERROR);
        }
    }

    /**
     * 保存字典类型实体
     * @param vo 字典类型实体对象
     */
    @Override
    public void saveEntity(SysDictTypeEntity vo) {
        baseMapper.insert(vo);
    }

    /**
     * 更新字典类型实体
     * @param vo 字典类型实体对象
     */
    @Override
    public void updateEntity(SysDictTypeEntity vo) {
        baseMapper.updateById(vo);
    }

    /**
     * 批量删除字典类型及其关联数据
     * @param idList 字典类型ID列表
     */
    @Override
    @Transactional
    public void delete(List<Long> idList) {
        baseMapper.deleteBatchIds(idList);
        dictDataService.deleteByDictTypeId(idList);
    }

    /**
     * 根据ID获取字典类型
     * @param id 字典类型ID
     * @return 字典类型实体对象
     */
    @Override
    public SysDictTypeEntity getById(String id) {
        return baseMapper.selectById(id);
    }

    /**
     * 根据字典类型名称获取字典数据
     * @param params 字典类型名称
     * @return 字典数据实体列表
     */
    @Override
    public List<SysDictDataEntity> getDictDataByDictTypeName(String params) {
        LambdaQueryWrapper<SysDictTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(SysDictTypeEntity::getDictName, params);
        SysDictTypeEntity dictTypeEntity = baseMapper.selectOne(queryWrapper);
        return dictDataService.getDictDataByDictTypeId(dictTypeEntity.getId());
    }

    /**
     * 获取所有字典列表
     * @return 系统字典视图对象列表，包含字典类型和对应的数据列表
     */
    @Override
    public List<SysDictVO> getDictList() {
        // 全部字典类型列表
        List<SysDictTypeEntity> typeList = this.list(Wrappers.emptyWrapper());

        // 全部字典数据列表
        QueryWrapper<SysDictDataEntity> query = new QueryWrapper<SysDictDataEntity>().orderByAsc("sort");
        List<SysDictDataEntity> dataList = dictDataMapper.selectList(query);

        // 全部字典列表
        List<SysDictVO> dictList = new ArrayList<>(typeList.size());
        for (SysDictTypeEntity type : typeList) {
            SysDictVO dict = new SysDictVO();
            dict.setDictType(type.getDictType());
            for (SysDictDataEntity data : dataList) {
                if (type.getId().equals(String.valueOf(data.getDictTypeId()))) {
                    dict.getDataList().add(new DictData(data.getDictKey(), data.getDictValue(), data.getLabelClass(),data.getSort()));
                }
            }

            // 数据来源动态SQL
            if (type.getDictSource() == DictSourceEnum.SQL.getValue()) {
                // 增加动态列表
                String sql = type.getDictSql();
                try {
                    List<DictData> listForSql = dictDataMapper.getListForSql(sql);
                    dict.setDataList(listForSql);
                } catch (Exception e) {
                    log.error("增加动态字典异常: type=" + type, e);
                }
            }
            dictList.add(dict);
        }
        return dictList;
    }

}