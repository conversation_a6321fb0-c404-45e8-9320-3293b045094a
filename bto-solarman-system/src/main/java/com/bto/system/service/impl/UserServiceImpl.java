package com.bto.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.commons.constant.UserEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.handler.MapResultHandler;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.entity.Role;
import com.bto.commons.pojo.entity.User;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.vo.UserInfoVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.CreateExcelUtils;
import com.bto.commons.utils.PropertyUtils;
import com.bto.oauth.entity.UserLogin;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.redis.utils.RedisUtil;
import com.bto.system.dao.RoleMapper;
import com.bto.system.dao.UserMapper;
import com.bto.system.service.ProjectService;
import com.bto.system.service.RoleService;
import com.bto.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import static com.bto.commons.constant.RedisKey.RESET_PASSWD_CODE;

/**
 * 用户服务实现类
 * 实现用户相关的业务操作，包括用户的增删改查、密码管理、角色分配、导入导出等功能的具体实现
 * <AUTHOR>
 * @date 2023/4/20 14:46
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    @Autowired
    private UserMapper userMapper;
    @Resource
    private RoleService roleService;
    @Resource
    private ProjectService projectService;
    @Resource
    private GlobalParamUtil globalParamUtil;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 根据合同ID获取用户信息
     * 通过合同唯一标识符获取关联的用户信息
     * @param contractId 合同唯一标识符
     * @return 用户信息视图对象
     */
    @Override
    public UserInfoVO getUserByContractId(String contractId) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        UserQueryDTO query = new UserQueryDTO();
        query.setContractId(contractId);
        query.setOrder("t1.create_time");
        Page<UserInfoVO> page = getUserList(query, userInfo);
        if (page.getTotal() > 0) {
            return page.getRecords().get(0);
        }
        return null;
    }

    /**
     * 用户列表多条件分页查询
     * 根据查询条件和用户权限分页获取用户信息列表
     * @param query 用户查询条件对象，包含分页参数、用户名、状态等筛选条件
     * @param userInfo 用户请求参数对象，包含当前用户身份和权限信息
     * @return 分页后的用户信息视图对象列表
     */
    @Override
    public Page<UserInfoVO> getUserList(UserQueryDTO query, RequireParamsDTO userInfo) {
        Page<UserInfoVO> userPage = new Page<>(query.getCurrentPage(), query.getPageSize());
        Page<UserInfoVO> page = userMapper.getUserList(userPage, query, userInfo);
        return page;
    }

    /**
     * 新增用户
     * 创建新的用户信息并保存到数据库
     * @param userInfo 用户信息数据传输对象，包含用户名、密码、联系方式等基本信息
     * @return 新增成功返回1，失败返回0
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer addUser(UserInfoDTO userInfo) {

        User dbUserName = this.lambdaQuery().eq(User::getUserName, userInfo.getUserName()).one();
        if (dbUserName != null) {
            throw new BusinessException("用户名已存在");
        }

        User user = new User();
        if (StrUtil.isBlank(userInfo.getUserUid())) {
            String userUid = userMapper.generateUid("user");
            user.setUserUid(userUid);
        } else {
            user.setUserUid(userInfo.getUserUid());
        }
        // user.setUserUid(UUID.randomUUID().toString().toUpperCase());
        user.setCreateTime(DateUtil.date());
        BeanUtils.copyProperties(userInfo, user, PropertyUtils.getNullPropertyNames(userInfo));
        Integer count = userMapper.insert(user);

        // 如添加成功并且是个人用户
        if (count > 0 && user.getUserType().equals(UserEnum.USER_OF_INDIVIDUAL.getCode()) && userInfo.getExtraPlant() != null) {
            Map<String, String> extraPlant = userInfo.getExtraPlant();
            Set<String> plantIds = extraPlant.keySet();
            if (CollUtil.isEmpty(plantIds)) {
                return count;
            }
            ((UserService) AopContext.currentProxy()).addUserHasPlant(user.getUserUid(), plantIds);
        }
        return count;
    }

    /**
     * 添加用户电站关联
     * 为用户添加电站关联关系，不删除原有关系
     * @param userUid 用户唯一标识符
     * @param plantIds 电站ID集合
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addUserHasPlant(String userUid, Set<String> plantIds) {
        userMapper.addUserHasPlant(userUid, plantIds);
    }

    /**
     * 检查用户电话
     *
     * @param phone 电话
     * @return boolean
     * <AUTHOR>
     * @since 2024-01-09 15:48:31
     */
    private boolean checkUserPhone(String phone) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUserPhone, phone);
        User user = this.getOne(wrapper);
        if (user != null) {
            return true;
        }
        return false;
    }

    /**
     * 修改用户
     * 更新现有用户的基本信息
     * @param userInfo 用户信息数据传输对象，包含用户ID和需要更新的用户信息
     * @return 修改成功返回1，失败返回0
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer editUser(UserInfoDTO userInfo) {
        // 如果参数没有uid 则修改自己
        if (userInfo.getUserUid() == null) {
            RequireParamsDTO require = globalParamUtil.getUserInfo();
            userInfo.setUserUid(require.getUserUid());
        }

        String userPhone = userInfo.getUserPhone();
        if (userPhone != null) {
            LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(User::getUserPhone, userPhone)
                    .ne(User::getUserUid, userInfo.getUserUid());
            User user = this.getOne(wrapper);
            if (user != null) {
                throw new BusinessException("手机号已存在");
            }
        }

        // 判断用户是否存在
        User user = userMapper.selectById(userInfo.getUserUid());
        if (user != null) {
            BeanUtils.copyProperties(userInfo, user, PropertyUtils.getNullPropertyNames(userInfo));
            // 更新用户
            int count = userMapper.updateById(user);
            // 如更新成功并且是个人用户
            if (count > 0 && user.getUserType().equals(UserEnum.USER_OF_INDIVIDUAL.getCode())) {
                Map<String, String> extraPlant = userInfo.getExtraPlant();

                if (extraPlant != null) { // 添加空值检查
                    Set<String> plantIds = extraPlant.keySet();
                    ((UserService) AopContext.currentProxy()).updateUserHasPlant(userInfo.getUserUid(), plantIds);
                }
            }
            return count;
        } else {
            throw new BusinessException(
                    ResultEnum.USER_NON_EXISTENT.getCode(),
                    ResultEnum.USER_NON_EXISTENT.getMessage()
            );
        }
    }

    /**
     * 更新用户拥有电站  先删除再添加
     *
     * @param userUid  用户uid
     * @param plantIds 电站ID集合
     * <AUTHOR>
     * @since 2024-11-01 13:55:50
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateUserHasPlant(String userUid, Set<String> plantIds) {
        userMapper.removeUserHasPlant(userUid);
        if (CollUtil.isNotEmpty(plantIds)) {
            userMapper.addUserHasPlant(userUid, plantIds);
        }
    }

    /**
     * 删除用户
     * 根据用户ID删除指定的用户信息
     * @param userUid 用户唯一标识符
     * @return 删除成功返回1，失败返回0
     */
    @Override
    public Integer deleteUser(String userUid) {
        // 判断用户是否存在
        User user = userMapper.selectById(userUid);
        if (user != null) {
            // 更新用户
            int count = userMapper.deleteById(userUid);
            return count;
        } else {
            throw new BusinessException(
                    ResultEnum.USER_NON_EXISTENT.getCode(),
                    ResultEnum.USER_NON_EXISTENT.getMessage()
            );
        }
    }

    /**
     * 查询用户信息
     * 根据用户登录信息查询用户的详细信息
     * @param userLogin 用户登录对象，包含登录凭证信息
     * @return 用户信息数据传输对象
     */
    @Override
    public UserDTO selectUserInfo(UserLogin userLogin) {
        UserDTO userInfo = userMapper.selectUserByUserInfo(userLogin);
        if (UserEnum.USER_OF_INDIVIDUAL.getCode().equals(userInfo.getUserType()) && StrUtil.isNotBlank(userInfo.getUserUid())) {
            // 根据用户id查询电站id
            userInfo.setPlantUidList(userMapper.getPlantUidList(userInfo.getUserUid()));
        }
        return userInfo;
    }

    /**
     * 根据用户Uid查询所属所有电站
     * 获取指定用户所关联的所有电站唯一标识符
     * @param userUid 用户唯一标识符
     * @return 电站UID字符串列表
     */
    @Override
    public List<String> getPlantUidList(String userUid) {
        List<String> plantUidList = userMapper.getPlantUidList(userUid);
        return plantUidList;
    }

    /**
     * 用户密码修改
     * 修改用户的登录密码，需要验证原密码
     * @param userPasswordDTO 密码修改数据传输对象，包含原密码和新密码
     * @param userInfo 用户请求参数对象，包含当前用户身份信息
     * @return 修改成功返回1，失败返回0
     */
    @Override
    public Integer updatePassword(UserPasswordDTO userPasswordDTO, RequireParamsDTO userInfo) {
        QueryWrapper<User> userQuery = new QueryWrapper<>();
        userQuery.eq("user_uid", userInfo.getUserUid());
        userQuery.eq("user_password", userPasswordDTO.getOldPass());
        User user = userMapper.selectOne(userQuery);
        if (user == null) {
            throw new BusinessException(
                    ResultEnum.PASSWORD_VERIFICATION_FAILED.getCode(),
                    ResultEnum.PASSWORD_VERIFICATION_FAILED.getMessage()
            );
        }
        user.setUserPassword(userPasswordDTO.getNewPass());
        return userMapper.updateById(user);
    }

    /**
     * 根据用户ID获取用户名和电话号码
     * 通过用户唯一标识符查询用户的基本联系信息
     * @param userUid 用户唯一标识符
     * @return 包含用户名和电话号码的映射集合
     */
    @Override
    public Map<String, String> getUserNameAndPhoneByUserUid(String userUid) {
        QueryWrapper<User> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("user_uid", userUid);
        User user = userMapper.selectOne(userQueryWrapper);
        if (user == null) {
            return null;
        }
        HashMap<String, String> userNameAndPhone = new HashMap<>();
        userNameAndPhone.put("userName", user.getUserName());
        userNameAndPhone.put("userPhone", user.getUserPhone());
        return userNameAndPhone;
    }

    /**
     * 根据用户id查询用户信息
     * 通过用户唯一标识符获取用户的详细信息
     * @param userUid 用户唯一标识符
     * @return 用户信息视图对象
     */
    @Override
    public UserInfoVO getUserInfoByUserUid(String userUid) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUserUid, userUid);
        User user = userMapper.selectOne(queryWrapper);
        if (user == null) {
            throw new BusinessException(ResultEnum.USER_NON_EXISTENT);
        }
        UserInfoVO userInfoVO = BeanUtil.copyProperties(user, UserInfoVO.class);

        if (userInfoVO.getUserType().equals(UserEnum.USER_OF_INDIVIDUAL.getName())) {
            Map<String, String> extraPlant = getExtraPlant(userUid);
            userInfoVO.setExtraPlant(extraPlant);
        }

        return userInfoVO;
    }

    /**
     * Excel导入用户
     * 通过Excel文件批量导入用户信息
     * @param file Excel文件对象，包含批量用户数据
     * @param password 默认密码，为导入用户统一设置
     * @return 统一响应结果，包含导入成功或失败的信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importByExcel(MultipartFile file, String password) {

        ArrayList<User> users = new ArrayList<>();
        List<User> insertUserList;
        try {
            EasyExcel.read(file.getInputStream(), ExcelImportUserDTO.class, new PageReadListener<ExcelImportUserDTO>(dataList -> {
                Map<String, Long> roleMap = roleService.getRoleMap(RoleQueryDTO.builder().build());
                Map<String, Integer> projectMap = projectService.getProjectMap();
                for (ExcelImportUserDTO data : dataList) {
                    String projectName = data.getProjectName();
                    data.setProjectID(projectMap.get(projectName));
                    String roleName = data.getRoleName();
                    data.setRoleID(roleMap.get(roleName));
                    User user = BeanUtil.copyProperties(data, User.class);
                    // 初始化市数据
                    user.setUserPassword(password);
                    user.setUserStatus(Integer.parseInt(UserEnum.USER_STATUS_ENABLE.getCode()));
                    // 检查是否存在相同的用户名或手机号
                    if (users.stream().anyMatch(u -> u.getUserName().equals(user.getUserName()) || u.getUserPhone().equals(user.getUserPhone()))) {
                        // 存在相同数据，抛出异常
                        throw new BusinessException(ResultEnum.DATA_EXISTED_ERROR.getCode(), "Excel中存在相同的用户名或手机号：" + user.getUserName() + ", " + user.getUserPhone());
                    }
                    users.add(user);
                }
            })).sheet().doRead();
        } catch (IOException e) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), e.getMessage());
        }
        Set<String> userNames = users.stream().map(User::getUserName).collect(Collectors.toSet());
        Set<String> userPhones = users.stream().map(User::getUserPhone).collect(Collectors.toSet());
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(User::getUserName, userNames).or().in(User::getUserPhone, userPhones);
        List<User> dbData = userMapper.selectList(wrapper);

        int start = users.size();
        // 如果有重复的数据  就去除重复的数据
        List<User> filteredUsers = new ArrayList<>();
        if (CollUtil.isNotEmpty(dbData)) {
            Set<String> existingUsernames = dbData.stream().map(User::getUserName).collect(Collectors.toSet());
            Set<String> existingPhoneNumbers = dbData.stream().map(User::getUserPhone).collect(Collectors.toSet());

            insertUserList = users.stream()
                    .peek(user -> {
                        if (existingUsernames.contains(user.getUserName()) || existingPhoneNumbers.contains(user.getUserPhone())) {
                            filteredUsers.add(user);
                        }
                    })
                    .filter(user -> !existingUsernames.contains(user.getUserName()) && !existingPhoneNumbers.contains(user.getUserPhone()))
                    .collect(Collectors.toList());

        } else {
            insertUserList = users;
        }

        // 插入数据库
        if (CollUtil.isNotEmpty(insertUserList)) {
            try {
                this.saveBatch(insertUserList);
            } catch (Exception e) {
                Throwable cause = e.getCause();
                if (cause instanceof SQLException) {
                    String message = cause.getMessage();
                    if (message.contains("Duplicate entry")) {
                        // 解析出用户名和手机号
                        String[] parts = message.split("'");
                        if (parts.length >= 2) {
                            String item = parts[1];
                            throw new BusinessException(ResultEnum.DATA_EXISTED_ERROR.getCode(),
                                    "存在已删除的相同的用户名或手机号：" + item);
                        }
                    }
                }
                // 如果无法解析，抛出通用异常
                throw new BusinessException(ResultEnum.OPERATION_FAILED.getCode(), "数据插入失败,请联系管理员");
            }
        }
        int end = insertUserList.size();

        return Result.success(String.format("共 %d 条数据，成功插入 %d 条，失败 %d 条。", start, insertUserList.size(), start - end), filteredUsers);
    }

    /**
     * 导出用户数据
     * 根据查询条件导出用户信息到Excel文件
     * @param query 用户查询条件对象
     * @param response HTTP响应对象，用于输出Excel文件流
     */
    @Override
    public void export(UserQueryDTO query, HttpServletResponse response) {
        query.setOrder(StrUtil.isEmpty(query.getOrder()) ? "createTime" : query.getOrder());
        Page<UserInfoVO> page = this.getUserList(query, globalParamUtil.getUserInfo());
        List<UserInfoVO> records = page.getRecords();
        CreateExcelUtils.exportToExcel(records, UserInfoVO.class, "user_info", response);

    }

    /**
     * 角色分配用户
     * 为用户分配系统角色，建立用户与角色的关联关系
     * @param roleUserDTO 角色用户数据传输对象，包含角色ID和用户ID集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignRoles(RoleUserDTO roleUserDTO) {
        String roleId = roleUserDTO.getRoleId();
        roleUserDTO.getUserIdList().forEach(userId -> {
            User user = userMapper.selectById(userId);
            if (Objects.isNull(user)) {
                throw new BusinessException("用户Id：" + userId + "不存在");
            }
            if (roleId.equals(user.getRoleID())) {
                throw new BusinessException("用户：" + user.getUserName() + "已设置该角色，请不要重复设置");
            }
            Role role = roleMapper.selectById(roleId);
            if (Objects.isNull(role)) {
                throw new BusinessException("角色Id：" + roleId + "不存在");
            }
            user.setRoleID(roleId);
            userMapper.updateById(user);
        });
    }

    /**
     * 通过电话检查用户是否存在
     * 根据手机号码查询用户是否已注册
     * @param phoneNumber 电话号码
     * @return 包含检查结果和提示信息的映射集合
     * <AUTHOR>
     * @since 2024-01-08 14:15:23
     */
    @Override
    public HashMap<Boolean, String> checkUserExistByPhone(String phoneNumber) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(User::getUserPhone, phoneNumber);
        User user = this.getOne(wrapper);
        HashMap<Boolean, String> map = new HashMap<>();
        if (user == null) {
            map.put(Boolean.FALSE, "手机号未注册");
            return map;
        }

        if (user.getUserStatus().equals(0)) {
            map.put(Boolean.FALSE, "该账号未启用");
        }

        map.put(Boolean.TRUE, "");
        return map;
    }

    /**
     * 通过验证码重置密码
     * 使用手机验证码重置用户密码
     * @param phoneNumber 用户手机号码
     * @param code 手机验证码
     * @param password 新密码
     * @return 统一响应结果，包含重置成功或失败的信息
     */
    @Override
    public Result resetPasswdByCode(String phoneNumber, String code, String password) {
        ResetPasswdDTO dto = (ResetPasswdDTO) redisUtil.get(RESET_PASSWD_CODE + phoneNumber);
        if (dto == null || !code.equals(dto.getCode())) {
            return Result.failed("验证码错误");
        }

        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(User::getUserPhone, phoneNumber)
                .eq(User::getUserStatus, 1);

        User user = this.getOne(wrapper);

        if (user == null) {
            return Result.failed("用户不存在");
        }
        user.setUserPassword(password);
        boolean success = this.updateById(user);
        if (success) {
            return Result.success(ResultEnum.OPERATION_SUCCESS);
        }
        return Result.failed(ResultEnum.OPERATION_FAILED);
    }

    /**
     * 获取用户布局配置
     * 获取指定用户的个性化界面布局配置
     * @param userId 用户唯一标识符
     * @return 布局配置JSON字符串
     */
    @Override
    public String getLayoutByUser(String userId) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUserUid, userId);
        User user = this.getOne(wrapper);
        if (user != null) {
            return user.getLayout();
        }
        return null;
    }

    /**
     * 更新用户布局配置
     * 保存用户的个性化界面布局配置
     * @param userId 用户唯一标识符
     * @param layout 布局配置JSON字符串
     * @return 统一响应结果，包含更新成功或失败的信息
     */
    @Override
    public Result updateLayoutByUser(String userId, String layout) {
        boolean update = this.lambdaUpdate().eq(User::getUserUid, userId).set(User::getLayout, layout).update();
        if (update) {
            return Result.success(ResultEnum.OPERATION_SUCCESS);
        }
        return Result.failed(ResultEnum.OPERATION_FAILED);
    }

    /**
     * 获取用户名称和ID映射
     * 批量获取指定用户ID对应的用户名称
     * @param userIds 用户ID列表
     * @return 用户名称和ID的映射集合
     */
    @Override
    public Map<String, String> getNameAndId(List<String> userIds) {
        List<User> list = this.lambdaQuery().in(User::getUserUid, userIds).list();
        return list.stream().collect(Collectors.toMap(User::getUserUid, User::getUserName));
    }

    /**
     * 获取用户额外电站信息
     * 获取指定用户的额外电站关联信息
     * @param userUid 用户唯一标识符
     * @return 额外电站信息的映射集合
     */
    @Override
    public Map<String, String> getExtraPlant(String userUid) {
        MapResultHandler<String, String> mapResultHandler = new MapResultHandler<>();
        userMapper.getExtraPlant(userUid, mapResultHandler);
        return mapResultHandler.getMap();
    }
}