package com.bto.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.commons.constant.NoticeStateEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.AddNoticeDTO;
import com.bto.commons.pojo.dto.NoticesQuery;
import com.bto.commons.pojo.entity.Notices;
import com.bto.commons.pojo.entity.ProjectHasNotices;
import com.bto.commons.pojo.entity.UserReadNotices;
import com.bto.commons.pojo.vo.NoticeDTO;
import com.bto.commons.pojo.vo.NoticesListVO;
import com.bto.commons.pojo.vo.NoticesVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.DateUtils;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.system.dao.NoticeMapper;
import com.bto.system.service.NoticeService;
import com.bto.system.service.UserReadNoticesService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.bto.commons.constant.ProjectTypeEnum.SUPER_ADMIN;

/**
 * 通知公告服务实现类
 * 提供通知公告的增删改查、已读状态管理等功能
 * <AUTHOR>
 * @date 2023/8/19 15:38
 */
@Service
@AllArgsConstructor
public class NoticeServiceImpl extends ServiceImpl<NoticeMapper, Notices> implements NoticeService {
    private final NoticeMapper noticeMapper;

    private final UserReadNoticesService userReadNoticesService;

    private final GlobalParamUtil globalParamUtil;

    /**
     * 分页获取通知公告列表
     * @param query 查询条件，包含分页参数、状态、标题等筛选条件
     * @return 通知公告分页结果
     */
    @Override
    public Page<NoticesVO> getNoticeList(NoticesQuery query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        NoticesListVO vo = new NoticesListVO();

        List<String> projectList = userInfo.getProjectList();

        Page<Notices> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        LambdaQueryWrapper<Notices> queryWrapper = new LambdaQueryWrapper<>();

        List<String> noticeIds = new ArrayList<>();
        List<ProjectHasNotices> projectHasNotices = new ArrayList<>();
        projectHasNotices = noticeMapper.selectProjectHasNotices(projectList);
        if (!userInfo.getProjectID().contains(SUPER_ADMIN.getProjectID().toString())) {

            if (CollUtil.isEmpty(projectHasNotices)) {
                return new Page<>();
            }
            noticeIds = projectHasNotices.stream().map(ProjectHasNotices::getNoticeId).collect(Collectors.toList());
            if (CollUtil.isEmpty(noticeIds)) {
                return new Page<>();
            }
        }

        queryWrapper
                .in(CollUtil.isNotEmpty(noticeIds), Notices::getId, noticeIds)
                .eq(StrUtil.isNotEmpty(query.getState()), Notices::getState, query.getState())
                .like(StrUtil.isNotEmpty(query.getTitle()), Notices::getTitle, query.getTitle())
                .like(StrUtil.isNotEmpty(query.getCreator()), Notices::getCreator, query.getCreator())
                .like(StrUtil.isNotEmpty(query.getContent()), Notices::getContent, query.getContent())
                .ge(StrUtil.isNotEmpty(query.getNoticeTime()), Notices::getEndTime, query.getNoticeTime())
                .le(StrUtil.isNotEmpty(query.getNoticeTime()), Notices::getStartTime, query.getNoticeTime())
                .between(StrUtil.isNotEmpty(query.getCreateStartTime()) && StrUtil.isNotEmpty(query.getCreatEndTime())
                        , Notices::getCreateTime, query.getCreateStartTime(), query.getCreatEndTime())
                .orderByDesc(Notices::getCreateTime);

        if (CollUtil.isNotEmpty(query.getClientId())) {
            queryWrapper.and(w -> {
                for (String client : query.getClientId()) {
                    w.or(ww -> ww.like(Notices::getClientId, client));
                }
            });
        }

        Page<Notices> result = noticeMapper.selectPage(page, queryWrapper);
        List<NoticesVO> voList = BeanUtil.copyToList(result.getRecords(), NoticesVO.class);
        for (NoticesVO notice : voList) {
            for (ProjectHasNotices projectHasNotice : projectHasNotices) {
                if (notice.getId().equalsIgnoreCase(projectHasNotice.getNoticeId())) {
                    notice.getProjectIds().add(projectHasNotice.getProjectId());
                }
            }
        }

        return new Page<NoticesVO>(query.getCurrentPage(), query.getPageSize(), page.getTotal()).setRecords(voList);
    }

    /**
     * 新增通知公告
     * @param addNotice 通知公告添加DTO对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addNotice(AddNoticeDTO addNotice) {
        Notices notice = new Notices();
        String noticeId = IdUtil.getSnowflakeNextIdStr();
        BeanUtil.copyProperties(addNotice, notice);
        notice.setId(noticeId);
        notice.setCreateTime(DateUtil.now());
        notice.setUpdateTime(DateUtil.now());
        noticeMapper.insert(notice);
        List<ProjectHasNotices> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(addNotice.getProjectIds())) {
            for (String projectId : addNotice.getProjectIds()) {
                list.add(new ProjectHasNotices(projectId, noticeId, DateUtil.now()));
            }
            noticeMapper.insertBatchNotices(list);
        }
    }

    /**
     * 编辑通知公告
     * @param notice 通知公告DTO对象
     * @param id 通知公告ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editNotice(NoticeDTO notice, String id) {
        Notices notices = new Notices();
        BeanUtil.copyProperties(notice, notices);
        notices.setId(id);
        notices.setUpdateTime(DateUtil.now());
        noticeMapper.updateById(notices);
        if (CollUtil.isNotEmpty(notice.getProjectIds())) {
            noticeMapper.deleteNoticeById(id);
            List<ProjectHasNotices> list = new ArrayList<>();
            for (String projectId : notice.getProjectIds()) {
                list.add(new ProjectHasNotices(projectId, id, DateUtil.now()));
            }
            noticeMapper.insertBatchNotices(list);
        }
    }

    /**
     * 删除通知公告
     * @param id 通知公告ID
     * @throws BusinessException 删除失败时抛出异常
     */
    @Override
    public void deleteNotice(String id) {
        LambdaUpdateWrapper<Notices> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Notices::getId, id);
        try {
            noticeMapper.delete(wrapper);
            noticeMapper.deleteNoticeById(id);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ResultEnum.SINGLE_DELETE_ERROR);
        }
    }

    /**
     * 根据项目ID获取当前公告
     * 获取指定客户端项目对应的当前有效公告列表
     * @param clientId 客户端ID列表
     * @return 公告列表视图对象，包含公告基本信息和阅读状态
     */
    @Override
    public NoticesListVO getNotices(List<String> clientId) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        NoticesListVO vo = new NoticesListVO();

        String userUid = userInfo.getUserUid();
        List<String> projectList = userInfo.getProjectList();
        List<String> noticeIds = new ArrayList<>();
        if (!userInfo.getProjectID().equals(SUPER_ADMIN.getProjectID().toString())) {
            noticeIds = noticeMapper.selectProjectHasNoticesIds(projectList);
            if (CollUtil.isEmpty(noticeIds)) {
                vo.setNotices(Collections.emptyList());
                vo.setUnReadCount(0);
                return vo;
            }
        }

        // 获取当前日期时间
        Date now = new Date();
        String dayStart = DateUtils.getDayStart(now);
        String dayEnd = DateUtils.getDayEnd(now);

        List<String> readNoticeIds = userReadNoticesService.lambdaQuery()
                .eq(UserReadNotices::getUserUid, userUid)
                .list().stream().map(UserReadNotices::getNoticesId).collect(Collectors.toList());

        int unReadCount = Math.toIntExact(this.lambdaQuery()
                // .like(Notices::getProjectIds, projectId)
                .in(CollUtil.isNotEmpty(noticeIds), Notices::getId, noticeIds)
                .eq(Notices::getState, NoticeStateEnum.ENABLE.getCode())
                .ge(Notices::getStartTime, dayStart)
                .ge(Notices::getEndTime, dayEnd)
                .notIn(CollUtil.isNotEmpty(readNoticeIds), Notices::getId, readNoticeIds)
                .count());

        // 获取已经开始的所有公告
        LambdaQueryWrapper<Notices> wrapper = new LambdaQueryWrapper<>();

        wrapper
                .in(!userInfo.getProjectID().contains(SUPER_ADMIN.getProjectID().toString()), Notices::getId, noticeIds)
                .eq(Notices::getState, NoticeStateEnum.ENABLE.getCode())
                .ge(Notices::getStartTime, dayStart)
                .ge(Notices::getEndTime, dayEnd)
                .notIn(CollUtil.isNotEmpty(readNoticeIds), Notices::getId, readNoticeIds)
                .orderByDesc(Notices::getCreateTime);


        if (CollUtil.isNotEmpty(clientId)) {
            wrapper.and(w -> {
                for (String client : clientId) {
                    w.or(ww -> ww.like(Notices::getClientId, client));
                }
            });
        }
        List<Notices> notices = noticeMapper.selectList(wrapper);
        List<NoticesVO> list = BeanUtil.copyToList(notices, NoticesVO.class);

        // 设置是否已读
        list.forEach(item -> {
            if (readNoticeIds.contains(item.getId())) {
                item.setUnRead(1);
            } else {
                item.setUnRead(0);
            }
        });

        vo.setNotices(list);
        vo.setUnReadCount(unReadCount);
        return vo;
    }

    /**
     * 获取公告详情
     * 根据公告ID和用户ID获取公告的详细内容
     * @param noticeId 公告唯一标识符
     * @param userUid 用户唯一标识符
     * @return 公告实体对象，包含完整的公告内容
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Notices detail(String noticeId, String userUid) {
        UserReadNotices userReadNotices = new UserReadNotices(noticeId, userUid);
        List<UserReadNotices> list = userReadNoticesService.lambdaQuery()
                .eq(UserReadNotices::getNoticesId, noticeId)
                .eq(UserReadNotices::getUserUid, userUid).list();
        if (CollUtil.isEmpty(list)) {
            userReadNoticesService.save(userReadNotices);
        }
        return this.getById(noticeId);
    }

    /**
     * 标记所有公告为已读
     * 将当前用户的所有未读公告标记为已读状态
     * @param userInfo 用户请求参数对象，包含用户身份信息
     */
    @Override
    public void readAllNotices(RequireParamsDTO userInfo) {
        String userUid = userInfo.getUserUid();
        List<String> projectId = userInfo.getProjectID();
        // 获取当前日期时间
        String now = DateUtil.today();

        // 数据库公告ID列表
        List<String> dbReadNoticeIds = userReadNoticesService.lambdaQuery()
                .eq(UserReadNotices::getUserUid, userUid)
                .list().stream().map(UserReadNotices::getNoticesId).collect(Collectors.toList());

        // 获取已经开始的所有公告

        List<String> noticeIds = new ArrayList<>();
        if (!userInfo.getProjectID().equals(SUPER_ADMIN.getProjectID().toString())) {
            noticeIds = noticeMapper.selectProjectHasNoticesIds(projectId);
            if (CollUtil.isEmpty(noticeIds)) {
                return;
            }
        }
        LambdaQueryWrapper<Notices> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .in(!userInfo.getProjectID().equals(SUPER_ADMIN.getProjectID().toString()), Notices::getId, noticeIds)
                .eq(Notices::getState, NoticeStateEnum.ENABLE.getCode())
                .le(Notices::getStartTime, now)
                .ge(Notices::getEndTime, now)
                .orderByDesc(Notices::getCreateTime);

        List<String> allNoticeIds = noticeMapper.selectList(wrapper).stream().map(Notices::getId).collect(Collectors.toList());

        // 需要插入已读的公告
        Collection<String> insertList = CollUtil.subtract(allNoticeIds, dbReadNoticeIds);

        if (CollUtil.isNotEmpty(insertList)) {
            List<UserReadNotices> list = new ArrayList<>();
            for (String noticeId : insertList) {
                list.add(new UserReadNotices(noticeId, userUid));
            }
            userReadNoticesService.saveBatch(list);
        }
    }

    /**
     * 修改公告状态
     * 更改公告的发布状态（发布/撤回）
     * @param noticeId 公告唯一标识符
     * @param state 目标状态值（0：草稿，1：发布，2：撤回）
     */
    @Override
    public void changeState(String noticeId, String state) {
        state = state.equals(NoticeStateEnum.DISABLE.getCode()) ? NoticeStateEnum.ENABLE.getCode() : NoticeStateEnum.DISABLE.getCode();
        this.lambdaUpdate()
                .set(Notices::getState, state)
                .eq(Notices::getId, noticeId)
                .update();
    }
}
