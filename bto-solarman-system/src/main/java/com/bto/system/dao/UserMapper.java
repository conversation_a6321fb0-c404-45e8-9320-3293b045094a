package com.bto.system.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.handler.MapResultHandler;
import com.bto.commons.pojo.dto.UserDTO;
import com.bto.commons.pojo.dto.UserQueryDTO;
import com.bto.commons.pojo.entity.User;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.vo.UserInfoVO;
import com.bto.oauth.entity.UserLogin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * 用户数据访问层接口
 * 提供用户相关的数据库操作
 * <AUTHOR>
 * @date 2023/4/20 14:40
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    /**
     * 用户列表多条件分页查询
     * @param userPage 分页对象
     * @param query 用户查询条件
     * @param userInfo 用户信息参数
     * @return 用户分页列表
     */
    Page<UserInfoVO> getUserList(Page<UserInfoVO> userPage,
                                 @Param("query") UserQueryDTO query,
                                 @Param("userInfo") RequireParamsDTO userInfo);

    /**
     * 根据用户名获取用户信息
     * @param username 用户名
     * @return 用户信息
     */
    default User getByUsername(String username){
        return this.selectOne(new QueryWrapper<User>().eq("user_name", username));
    }

    /**
     * 查询用户信息
     * @param query 用户登录信息
     * @return 用户数据传输对象
     */
    UserDTO selectUserByUserInfo(UserLogin query);

    /**
     * 根据用户Uid查询所属所有电站
     * @param userUid 用户唯一标识
     * @return 电站唯一标识列表
     */
    List<String> getPlantUidList(@Param("userUid") String userUid);

    /**
     * 根据user_uid获取bto_user_has_plant数据  并且plant_uid 不在bpb表的
     *
     * @param userUid          用户uid
     * @param mapResultHandler 映射结果处理
     * <AUTHOR>
     * @since 2024-11-01 14:33:42
     */
    void getExtraPlant(@Param("userUid")String userUid, MapResultHandler<String, String> mapResultHandler);

    /**
     * 根据user_uid删除bto_user_has_plant数据  并且plant_uid 不在bpb表的
     *
     * @param userUid 用户uid
     * <AUTHOR>
     * @since 2024-11-01 14:32:07
     */
    void removeUserHasPlant(@Param("userUid") String userUid);

    /**
     * 添加用户与电站的关联关系
     * @param userUid 用户唯一标识
     * @param plantIds 电站唯一标识集合
     */
    void addUserHasPlant(@Param("userUid") String userUid,@Param("plantIds") Set<String> plantIds);

    /**
     * 根据合同ID获取用户信息
     * @param contractId 合同ID
     * @param userInfo 用户信息参数
     * @return 用户信息视图对象
     */
    UserInfoVO getUserByContractId(@Param("contractId") String contractId,@Param("userInfo") RequireParamsDTO userInfo);

    /**
     * 生成唯一标识符
     * @param type 类型标识
     * @return 生成的唯一标识符
     */
    @Select("SELECT fun_produce_uid(#{type})")
    String generateUid(@Param("type") String type);
}
