package com.bto.system.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 客户端Mapper接口
 * 用于管理OAuth客户端信息的数据库访问操作
 * <AUTHOR>
 * @date 2023/8/23 9:49
 */
@Mapper
public interface ClientMapper {

    /**
     * 获取所有客户端ID列表
     * 
     * @return 客户端ID字符串列表，包含系统中所有注册的客户端标识
     */
    @Select("select client_id from oauth_client_details")
    List<String> getClientIds();
}
