<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.system.dao.RoleMapper">
    <resultMap id="RoleInfoMap" type="com.bto.commons.pojo.vo.RoleInfoVO">
        <id property="roleID" column="roleID" jdbcType="VARCHAR"/>
        <result property="roleName" column="roleName" jdbcType="VARCHAR"/>
        <result property="roleRemark" column="roleRemark" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="createTime" jdbcType="VARCHAR"/>
        <collection property="menuList" ofType="java.lang.String" javaType="java.util.ArrayList">
            <result column="menUid" javaType="String"/>
        </collection>
    </resultMap>

    <select id="getRoleList" resultMap="RoleInfoMap">
        SELECT
        t1.id roleID,
        t1.role_name roleName,
        t1.role_remark roleRemark,
        t1.creator,
        t1.create_time createTime,
        v.menu_id menUid
        FROM bto_role t1
            LEFT JOIN v_role_has_menu v ON t1.id = v.role_id
        <where>
                t1.is_deleted = 0
            <if test="query.roleName!=null and query.roleName!=''">
                AND t1.role_name LIKE CONCAT('%',#{query.roleName},'%')
            </if>
        </where>
        GROUP BY menUid,roleID
    </select>
</mapper>