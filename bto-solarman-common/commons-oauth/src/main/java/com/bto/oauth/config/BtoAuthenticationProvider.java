package com.bto.oauth.config;

import com.bto.commons.exception.BusinessException;
import com.bto.oauth.service.UserDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/5/24 16:06
 */

@Component
public class BtoAuthenticationProvider implements AuthenticationProvider {
    @Autowired
    private UserDetailService userDetailService;

    /**
     *     认证方法
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        UsernamePasswordAuthenticationToken adminLoginToken = (UsernamePasswordAuthenticationToken) authentication;
        UserDetails userDetails = null;
        try {
            userDetails = userDetailService.loadUserByUsernameAndPassword(adminLoginToken);
        } catch (Exception e) {
            BusinessException be = (BusinessException) e;
                throw new BusinessException(be.getCode(), be.getMessage(), be.getData());
        }
        //匹配短信验证码。进行密码校验，最终需要返回一个验证过的Token
        return  new UsernamePasswordAuthenticationToken(userDetails, userDetails.getPassword(), userDetails.getAuthorities());
    }



    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
