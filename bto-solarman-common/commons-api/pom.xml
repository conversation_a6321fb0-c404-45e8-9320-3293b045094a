<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bto</groupId>
        <artifactId>bto-solarman-common</artifactId>
        <version>3.0.0</version>
    </parent>

    <artifactId>commons-api</artifactId>

    <properties>
        <java.version>1.8</java.version>
        <springfox.version>3.0.0</springfox.version>
        <springfox-swagger.version>2.10.5</springfox-swagger.version>
        <openfeign.version>2.2.5.RELEASE</openfeign.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.bto</groupId>
            <artifactId>commons-redis</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.bto</groupId>
            <artifactId>commons-core</artifactId>
            <version>3.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>${openfeign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-dysmsapi20170525</artifactId>
            <version>2.0.24</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-gateway-pop</artifactId>
            <version>0.1.5-beta</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-gateway-oss</artifactId>
            <version>0.1.5-beta</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-gateway-sls</artifactId>
            <version>0.1.5-beta</version>
        </dependency>
    </dependencies>

</project>