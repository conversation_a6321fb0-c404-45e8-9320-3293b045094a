package com.bto.api.feign.devicemanage;

import com.bto.api.feign.config.FeignTransmitAllHeaderInterceptor;
import com.bto.commons.pojo.dto.AddDeviceDTO;
import com.bto.commons.pojo.dto.IotCardQuery;
import com.bto.commons.pojo.dto.SmsInfoDTO;
import com.bto.commons.pojo.entity.BtoElectricityMeter;
import com.bto.commons.pojo.entity.InverterComponent;
import com.bto.commons.pojo.vo.PlantPowerVO;
import com.bto.commons.response.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/13 10:29
 */
@FeignClient(name = "solarman-device",
        configuration = FeignTransmitAllHeaderInterceptor.class,
        fallbackFactory = DeviceServiceClientFallbackFactory.class)
public interface DeviceServiceClient {

    @ApiOperation("初始化设备信息")
    @GetMapping("initial/{imei}")
    Result initial(@PathVariable("imei") String imei);


    /**
     * feign远程调用
     * -- 根据电站Uid获取所有设备ID
     *
     * @param plantUid
     * @return
     */
    @RequestMapping(value = "/deviceId/{plantUid}", method = RequestMethod.GET)
    Result getDevicesId(@PathVariable(name = "plantUid") String plantUid);

    /**
     * feign远程调用
     * -- 获取电站的在线逆变器数量
     *
     * @param plantUid
     * @return
     */
    @RequestMapping(value = "/deviceManage/onlineInverterStats/{plantUid}", method = RequestMethod.GET)
    Result getOnlineInverterStats(@PathVariable("plantUid") String plantUid);

    /**
     * feign远程调用
     * --查询单个电站所有逆变器实时告警信息
     * --根据电站Uid
     *
     * @param plantUid
     * @param status
     * @return
     */
    @RequestMapping(value = "/deviceManage/InverterAlarmLatestStatsByPlantUid/{plantUid}", method = RequestMethod.POST)
    Result getInverterAlarmLatestStatsByPlantUid(@PathVariable("plantUid") String plantUid, @RequestParam(value = "status") String status);

    /**
     * feign远程调用
     * --查询批量电站所有逆变器实时告警信息
     * --根据电站Uid数组
     *
     * @param plantUids
     * @return
     */
    @RequestMapping(value = "/deviceManage/InverterAlarmLatestStatsByPlantUids/", method = RequestMethod.POST)
    Result getInverterAlarmLatestStatsByPlantUids(@RequestParam("plantUids") List<String> plantUids);

    /**
     * feign远程调用
     * -- 查询电站装机容量信息
     * --根据电站Uid查询
     *
     * @param plantUid
     * @return
     */
    @RequestMapping(value = "/TodayPeakPower/{plantUid}", method = RequestMethod.GET)
    Result getTodayPeakPowerByPlantUid(@PathVariable("plantUid") String plantUid);


    /**
     * feign远程调用
     * --删除电站所属设备
     * --根据电站Uid
     *
     * @param plantUid
     * @return
     */
    @RequestMapping(value = "{plantUid}/deleteDevice/", method = RequestMethod.GET)
    Result deleteDeviceByPlantUid(@PathVariable("plantUid") String plantUid);


    /**
     * feign远程调用
     * --查询设备是否已被移除或失效（未激活）
     * --根据设备ID查询
     *
     * @param deviceId
     * @param isDeleted
     * @return
     */
    @RequestMapping(value = "{deviceId}/isExisted", method = RequestMethod.GET)
    Result deviceIsExisted(@PathVariable("deviceId") String deviceId, String isDeleted);

    /**
     * 根据电站编号查询电站所属所有逆变器SN
     *
     * @param plantUid
     * @return
     */
    @GetMapping("getInverterSNList/{plantUid}")
    Result getInverterSnList(@PathVariable(value = "plantUid") String plantUid);

    /**
     * 求电站当日峰值功率
     *
     * @param date
     * @param inverterSnList
     * @return
     */
    @GetMapping("getPlantMaxPower/{date}")
    Result getPlantMaxPower(@PathVariable(value = "date") String date,
                            @RequestParam(value = "inverterSnList") List<String> inverterSnList);

    /**
     * 查询物联网卡信息列表
     *
     * @param query
     * @param authorization
     * @return
     */
    @PostMapping("/getIotCardByPage")
    Result getIotCardByPage(@RequestBody IotCardQuery query,
                            @RequestHeader("Authorization") String authorization);

    /**
     * (批量)通过物联卡号查询告警短信信息
     *
     * @param cimiList
     * @return
     */
    @PostMapping("/getSmsInfo")
    Result<List<SmsInfoDTO>> getSmsInfo(@RequestParam("cimiList") List<String> cimiList);

    /**
     * 通过电站ID查询电站当天功率曲线
     */
    @PostMapping("/getTotalPowerListByPlantUid")
    List<PlantPowerVO> getTotalPowerListByPlantUid(@RequestParam("plantId") String plantId, @RequestParam("date") String date);

    @PostMapping("/operator/getCimiListByPhone")
    List<String> getCimiListByPhone(@RequestBody List<String> phoneNumbers);


    @ApiOperation("根据电站id,设备类型 返回设备id")
    @GetMapping("getListByType")
    Result<List<String>> getListByType(@RequestParam(value = "plantUid", required = false) String plantUid,
                                       @RequestParam(value = "type", defaultValue = "11-配电柜 12-传感器") String type);


    @ApiOperation("通过电表id获取电表信息")
    @GetMapping("ammeter/info")
    Result<List<BtoElectricityMeter>> getInfoById(@RequestParam("plantUid") String plantUid);

    @PostMapping("/addDevice")
    Result addDevice(@RequestBody AddDeviceDTO addDeviceDTO);

    @ApiOperation("添加逆变器组件")
    @PostMapping("/addInverterComponent")
    Result addInverterComponent(@RequestBody List<InverterComponent> list);
}

