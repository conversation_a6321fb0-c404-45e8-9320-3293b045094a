package com.bto.api.feign.statistics;

import com.bto.api.feign.config.FeignTransmitAllHeaderInterceptor;
import com.bto.commons.pojo.dto.PlantStatisticsExportFileDTO;
import com.bto.commons.pojo.vo.BatteryDivinerVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/12/11.
 */
@FeignClient(name = "solarman-statistics",
        configuration = FeignTransmitAllHeaderInterceptor.class,
        fallbackFactory = StatisticsServiceClientFallbackFactory.class)
public interface StatisticsServiceClient {

    @PostMapping(value = "api/export/getPlantFileInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public byte[] getPlantFileInfo(@RequestBody PlantStatisticsExportFileDTO query);

    @GetMapping(value = "api/plantStatistics/getElectricityBySevenDay/{plantId}")
    List<BatteryDivinerVO> getElectricityBySevenDay(@PathVariable("plantId") String plantId);

}