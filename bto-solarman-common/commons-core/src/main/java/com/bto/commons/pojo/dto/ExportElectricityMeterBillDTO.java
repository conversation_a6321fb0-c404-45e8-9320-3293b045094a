package com.bto.commons.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> by zhb on 2025/5/29.
 */

@Data
@ApiModel("导出电表电费账单参数")
public class ExportElectricityMeterBillDTO {

    @ApiModelProperty("电站id")
    private String plantUid;

    @ApiModelProperty("开始时间:yyyy-MM-dd")
    private String startTime;

    @ApiModelProperty("结束时间:yyyy-MM-dd")
    private String endTime;

    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String regexpDate;

    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String photovoltaicMeterId;

    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String powerMeterId;
}
