package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/6 9:58
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RadiantQuantityQuery {
    private List<String> weatherStationUidList;
    private List<String> cities;
    private String startTime;
    private String endTime;

    public RadiantQuantityQuery(List<String> cities, String startTime, String endTime) {
        this.cities = cities;
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
