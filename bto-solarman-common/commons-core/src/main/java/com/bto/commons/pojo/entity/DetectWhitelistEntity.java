package com.bto.commons.pojo.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

/**
 * 运维检测白名单
 *
 * <AUTHOR>
 * @since 1.0.0 2024-02-28
 */

@Data
@TableName("bto_detect_whitelist")
public class DetectWhitelistEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 电站uid
     */
    private String plantUid;

    /**
     * 逆变器SN
     */
    private String inverterSn;

    /**
     * 故障类型（
     DYJC  --  电压检测，
     DLJC  --  电流检测,
     ZCJC  --  组串检测，
     PVJC  --  PV  检测，
     DFDL  -- 低发电量检测
     ）
     */
    private String alarmType;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 逻辑删除（0 — 存在，1 — 删除）
     */
    private Integer isDeleted;

}