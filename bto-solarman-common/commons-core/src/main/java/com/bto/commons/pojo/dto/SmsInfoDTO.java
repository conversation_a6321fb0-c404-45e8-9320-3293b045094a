package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 物联网卡告警信息数据
 * <AUTHOR>
 * @date 2023/11/15 10:44
 */
@Data
@ApiModel("物联网卡告警信息数据")
public class SmsInfoDTO implements Serializable {
    private static final long serialVersionUID = -5357216133817760845L;
    @ApiModelProperty("物联卡号")
    private String cimi;
    @ApiModelProperty("用户名称")
    private String userName;
    @ApiModelProperty("用户手机号码")
    private String userPhone;
}
