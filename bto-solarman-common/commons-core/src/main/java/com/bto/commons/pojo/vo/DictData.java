package com.bto.commons.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "字典数据")
@AllArgsConstructor
@NoArgsConstructor
public class DictData {
    @Schema(description = "字典标签")
    private String dictLabel;

    @Schema(description = "字典值")
    private String dictValue;

    @Schema(description = "标签样式")
    private String labelClass;
    @Schema(description = "排序")
    private Integer sort;
}