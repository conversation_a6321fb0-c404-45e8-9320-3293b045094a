package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/5/12 11:44
 */
@Data
@ApiModel
public class AnalyzeObjectDataVO implements Serializable {
    private static final long serialVersionUID = -5956793178901745560L;
    @ApiModelProperty("电站Uid")
    private String plantUid;
    @ApiModelProperty("电站名称")
    private String plantName;
    @ApiModelProperty("实时功率")
    private String power;
    @ApiModelProperty("装机容量")
    private String plantCapacity;
    @ApiModelProperty("发电效率")
    private String electricityEfficiency;
    @ApiModelProperty("工作效率")
    private String workEfficiency;
    @ApiModelProperty("故障率")
    private String failureRate;
    @ApiModelProperty("日发电量")
    private String todayElectricity;
    @ApiModelProperty("总发电量")
    private String totalElectricity;
    @ApiModelProperty("建站时间")
    private String createTime;
    @ApiModelProperty("更新时间")
    private String updateTime;

    public void setPower(String power) {
        BigDecimal b1 = new BigDecimal(power);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.power = b2.toString();
    }

    public void setTodayElectricity(String todayElectricity) {
        BigDecimal b1 = new BigDecimal(todayElectricity);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.todayElectricity = b2.toString();
    }

    public void setTotalElectricity(String totalElectricity) {
        BigDecimal b1 = new BigDecimal(totalElectricity);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.totalElectricity = b2.toString();
    }

    public void setPlantCapacity(String plantCapacity) {
        BigDecimal b1 = new BigDecimal(plantCapacity);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(1000)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.plantCapacity = b2.toString();
    }
}
