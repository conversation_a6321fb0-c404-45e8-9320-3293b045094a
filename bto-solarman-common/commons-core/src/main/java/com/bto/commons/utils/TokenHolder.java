package com.bto.commons.utils;

/**
 *
 * <AUTHOR>
 * @date 2023-11-13
 */
public class TokenHolder {
    private static final ThreadLocal<String> tokenHolder = new ThreadLocal<>();
    private static Boolean TokenValidationFlag=true;

    public static Boolean getTokenValidationFlag() {
        return TokenValidationFlag;
    }

    public static void setTokenValidationFlag(Boolean tokenValidationFlag) {
        TokenValidationFlag = tokenValidationFlag;
    }

    public static void setToken(String token) {
        tokenHolder.set(token);
    }

    public static void removeToken() {
        tokenHolder.remove();
    }

    public static String getToken() {
        return tokenHolder.get();
    }
}
