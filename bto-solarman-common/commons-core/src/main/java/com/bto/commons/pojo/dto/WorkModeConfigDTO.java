package com.bto.commons.pojo.dto;

import com.bto.commons.pojo.entity.BtoSetParameter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class WorkModeConfigDTO {

    @ApiModelProperty(value = "逆变器SN，不能为空", required = true)
    private String deviceSn;

    @ApiModelProperty(
        value = "用户模式：1-自用模式，2-分时模式（需设置充放电计划），3-备电模式（可设置保留电量），4-被动模式（强制指定功率），5-光伏模式",
        allowableValues = "1,2,3,4,5",
        required = true
    )
    private Integer accUserMode;

    @ApiModelProperty(value = "充电计划列表（最多7条）", required = false)
    private List<BtoSetParameter.ElectricityPlan> chargeList;

    @ApiModelProperty(value = "放电计划列表（最多7条）", required = false)
    private List<BtoSetParameter.ElectricityPlan> dischargeList;

    @ApiModelProperty(value = "充电计划列表，最多7条计划", hidden = true)
    @JsonIgnore
    private String chargeListJsonStr;

    @ApiModelProperty(value = "放电计划列表，最多7条计划", hidden = true)
    @JsonIgnore
    private String dischargeListJsonStr;

}