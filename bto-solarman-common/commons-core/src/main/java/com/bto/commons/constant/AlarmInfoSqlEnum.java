package com.bto.commons.constant;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/3/7 8:36
 */
public enum AlarmInfoSqlEnum {

    ABNORMAL("1","pv % 无电流"),
    PV_AND_DC_ABNORMAL("2","PV%电压异常"),
    // DC_NUMBER_ABNORMAL("3","DC 数量不一致"),
    // NO_INFO("4","无组串信息"),
    // MISSING_INFO("5","组串缺失"),
    // ABNORMAL_OF_ELECTRICITY_ATTENUATION("6","发电量衰减异常"),
    // ABNORMAL_OF_ELECTRICITY("7","发电量异常")

    ;


    private final String code;
    private final String name;


    AlarmInfoSqlEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code){
        for(AlarmInfoSqlEnum item:AlarmInfoSqlEnum.values()){
            if(item.getCode().equals(code)){
                return item.getName();
            }
        }
        return "";
    }
}
