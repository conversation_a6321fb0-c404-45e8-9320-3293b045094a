package com.bto.commons.handler;

import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.ServletException;
import javax.validation.ValidationException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 统一捕获异常处理
 *
 * <AUTHOR>
 * @date 2023/3/29 10:46
 */

@Slf4j
@RestControllerAdvice
@AllArgsConstructor
public class CommonExceptionHandler {

    private final StringRedisTemplate redisTemplate;

    private static final String UPGRADE_STATUS_KEY = "system:upgrade:status";

    /**
     * 业务异常捕获
     *
     * @param businessException
     * @return Result
     */
    @ExceptionHandler({BusinessException.class})
    @ResponseBody
    public Result<?> handlerBusinessException(BusinessException businessException) {
        // 检查系统是否在升级中
        Boolean isUpgrading = checkSystemUpgrading();

        if (Boolean.TRUE.equals(isUpgrading)) {
            log.warn("系统升级中，异常已被捕获: {}", businessException.getMessage());
            return Result.success("系统升级中，请稍后重试");
        }
        log.error(businessException.getMessage(), businessException);
        return Result.instance(businessException.getCode(), businessException.getMessage(),businessException.getData());
    }

    /**
     * 访问权限异常
     *
     * @param forbiddenException 访问权限失败类型的枚举类
     * @return Result
     */
    // @ExceptionHandler({ForbiddenException.class})
    // public Result<?> handleForbidenException(ForbiddenException forbiddenException) {
    //     log.error(forbiddenException.getMessage(), forbiddenException);
    //     return Result.failed(ResultEnum.ACCESS_AUTHORIZATION_FAILED);
    // }

    @ExceptionHandler({HttpMessageNotReadableException.class})
    public Result<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException httpMessageNotReadableException){
        log.error(httpMessageNotReadableException.getMessage(),httpMessageNotReadableException);
        return Result.failed(ResultEnum.REQUIREDPARAM_EMPTY );
    }


    /**
     * 顶级异常捕获，当其他异常无法处理时选择使用
     *
     * @param exception
     * @return
     */
    @ExceptionHandler({Exception.class})
    public Result<?> handleException(Exception exception) {

        // 检查系统是否在升级中
        Boolean isUpgrading = checkSystemUpgrading();

        if (Boolean.TRUE.equals(isUpgrading)) {
            log.warn("系统升级中，异常已被捕获: {}", exception.getMessage());
            return Result.success("系统升级中，请稍后重试");
        }

        log.error(exception.getMessage(), exception);
        return Result.failed(ResultEnum.SYSTEM_RUNTIME_FAILED);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<?> handleValidationException(MethodArgumentNotValidException ex) {
        // 获取所有验证错误
        List<ObjectError> errors = ex.getBindingResult().getAllErrors();
        StringBuilder errorMsg = new StringBuilder();
        for (ObjectError error : errors) {
            errorMsg.append(error.getDefaultMessage()).append("; ");
        }
        return Result.failed(errorMsg.toString());
    }

    @ExceptionHandler(BindException.class)
    public Result<?> handleBindException(BindException ex) {
        // 获取所有验证错误
        List<ObjectError> errors = ex.getBindingResult().getAllErrors();
        StringBuilder errorMsg = new StringBuilder();
        for (ObjectError error : errors) {
            errorMsg.append(error.getDefaultMessage()).append("; ");
        }
        return Result.failed(errorMsg.toString());
    }


    private Boolean checkSystemUpgrading() {
        boolean isUpgrading = false;
        try {
            // 先从Redis获取
            String status = redisTemplate.opsForValue().get(UPGRADE_STATUS_KEY);
            if (status != null) {
                return Boolean.parseBoolean(status);
            } else {
                // 存入Redis
                redisTemplate.opsForValue().set(UPGRADE_STATUS_KEY, String.valueOf(isUpgrading));
            }
            return isUpgrading;
        } catch (Exception e) {
            log.error("检查系统升级状态失败", e);
            return false;
        }
    }

    /**
     * 捕获已知的系统级别异常
     *
     * @param exception
     * @return
     */
    @ExceptionHandler({ServletException.class})
    public Result<?> handlerKnownException(Exception exception) {
        log.error(exception .getMessage(), exception);
        return Result.failed(exception.getMessage());
    }
    /**
     * 捕获处理程序访问被拒绝异常
     *
     * @param exception
     * @return
     */
    @ExceptionHandler({AccessDeniedException.class})
    public Result<?> handlerAccessDeniedException(AccessDeniedException exception) {
        return Result.instance(ResultEnum.ACCESS_REFUSED_NO_AUTHORITY);
    }


    /**
     * 处理参数校验
     *
     * @param ex 异常
     * @return {@link Result }<{@link ? }>
     * <AUTHOR>
     * @since 2024-01-27 17:33:03
     */
    @ExceptionHandler(ValidationException.class)
    public Result<?> bindException(ValidationException ex) {
        String msg = ex.getMessage();

        // 生成返回消息
        String resultMsg = Arrays.stream(msg.split(","))
                .map(item -> item.split(":")[1].trim())
                .collect(Collectors.joining(","));

        return Result.failed(ResultEnum.REQUESTPARAM_ERROR.getCode(), resultMsg);
    }



}
