package com.bto.commons.pojo.vo;

import com.bto.commons.utils.DateUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 空间管理
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-26
 */
@Data
@Schema(description = "空间管理")
public class BtoUserRoomVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "用户uid")
    private String userUid;

    @Schema(description = "空间名称")
    private String room;

    @Schema(description = "排序字段")
    private Integer order;

    @Schema(description = "数据创建时间", hidden = true)
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createTime;

    @Schema(description = "数据更新时间", hidden = true)
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date updateTime;


}