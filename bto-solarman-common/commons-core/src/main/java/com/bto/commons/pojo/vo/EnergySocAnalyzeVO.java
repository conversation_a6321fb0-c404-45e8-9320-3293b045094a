package com.bto.commons.pojo.vo;

import com.bto.commons.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> by zhb on 2025/2/12.
 */
@Data
@ApiModel("电池SOC分析")
public class EnergySocAnalyzeVO {

    @ApiModelProperty("电池SOC")
    private String soc;

    @ApiModelProperty("时间")
    @JsonFormat(pattern = DateUtils.DATE_HOUR_PATTERN)
    private Date time;


}
