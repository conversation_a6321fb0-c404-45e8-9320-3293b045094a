package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
* 采购逆变器信息
* @TableName bto_inverter_order
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("bto_inverter_order")
public class InverterOrder implements Serializable {

    /**
    * 自增id
    */
    @NotNull(message="[自增id]不能为空")
    @ApiModelProperty("自增id")
    private Long id;
    /**
    * 逆变器SN
    */
    @NotBlank(message="[逆变器SN]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("逆变器SN")
    @Length(max= 50,message="编码长度不能超过50")
    private String inverterSn;
    /**
    * 厂家
    */
    @NotBlank(message="[厂家]不能为空")
    @Size(max= 25,message="编码长度不能超过25")
    @ApiModelProperty("厂家")
    @Length(max= 25,message="编码长度不能超过25")
    private String manufacturer;
    /**
    * 系列型号
    */
    @NotBlank(message="[系列型号]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("系列型号")
    @Length(max= 255,message="编码长度不能超过255")
    private String seriesModel;
    /**
    * 电路数
    */
    @ApiModelProperty("电路数")
    private Integer circuit;
    /**
    * 创建时间
    */
    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 更新时间
    */
    @NotNull(message="[更新时间]不能为空")
    @ApiModelProperty("更新时间")
    private Date updateTime;

}
