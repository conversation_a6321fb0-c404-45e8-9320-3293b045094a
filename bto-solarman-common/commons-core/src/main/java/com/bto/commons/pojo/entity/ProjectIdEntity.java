package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("bto_project_id")
@ApiModel(value = "BtoProjectId对象", description = "工程师调用项目ID")
public class ProjectIdEntity {

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "资方（或一级代理）,代理公司")
    private String agencyCompany;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    @ApiModelProperty(value = "数据创建时间")
    private Date createTime;

    @ApiModelProperty(value = "数据更新时间")
    private Date updateTime;
}
