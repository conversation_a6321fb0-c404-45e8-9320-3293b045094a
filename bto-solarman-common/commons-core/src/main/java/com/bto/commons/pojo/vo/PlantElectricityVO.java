package com.bto.commons.pojo.vo;

import com.bto.commons.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/4/25 15:00
 */
@Data
@ApiModel
public class PlantElectricityVO implements Serializable {
    private static final long serialVersionUID = 1546854790428901237L;
    @ApiModelProperty("最大工作效率的电站名")
    private String plantName;
    @ApiModelProperty("最大工作效率")
    private String maxEfficiency;
    @ApiModelProperty("平均工作效率")
    private String averageEfficiency;
    @ApiModelProperty("总功率")
    private String totalPower;
    @ApiModelProperty("日发电量")
    private String todayElectricity;
    @ApiModelProperty("月发电量")
    private String monthElectricity;
    @ApiModelProperty("年发电量")
    private String yearElectricity;
    @ApiModelProperty("总发电量")
    private String totalElectricity;
    @ApiModelProperty("总装机容量")
    private String plantCapacity;
    @ApiModelProperty("日CO2减排")
    private String todayCo2;
    @ApiModelProperty("累计煤炭减排")
    private String totalCocal;
    @ApiModelProperty("累计CO2减排")
    private String totalCo2;
    @ApiModelProperty("累计植树数量")
    private String treeNum;
    @ApiModelProperty("故障率")
    private String failureRate;
    @ApiModelProperty("日等效小时")
    private String dailyEfficiencyPerHour;

    @ApiModelProperty("用户创建日期")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date userCreateTime;

    @ApiModelProperty("用户累计陪伴时间（天）")
    private Integer userAccumulatedDays;

    {
        this.userAccumulatedDays = 0;
        this.plantName = "0";
        this.maxEfficiency = "0";
        this.averageEfficiency = "0";
        this.totalPower = "0";
        this.todayElectricity = "0";
        this.monthElectricity = "0";
        this.yearElectricity = "0";
        this.totalElectricity = "0";
        this.plantCapacity = "0";
        this.todayCo2 = "0";
        this.totalCocal = "0";
        this.totalCo2 = "0";
        this.treeNum = "0";
        this.failureRate = "0";
        this.dailyEfficiencyPerHour = "0";
    }

    public String getMaxEfficiency() {
        return maxEfficiency;
    }

    public String getFailureRate() {
        return failureRate;
    }

    public void setDailyEfficiencyPerHour(String dailyEfficiencyPerHour) {
        BigDecimal b1 = new BigDecimal(dailyEfficiencyPerHour).setScale(2, RoundingMode.HALF_UP);
        this.dailyEfficiencyPerHour = b1.toString();
    }

    public String getDailyEfficiencyPerHour() {
        return dailyEfficiencyPerHour;
    }

    public void setMaxEfficiency(String maxEfficiency) {
        BigDecimal b1 = new BigDecimal(maxEfficiency).setScale(2, RoundingMode.HALF_UP);
        this.maxEfficiency = b1.toString();
    }

    public String getAverageEfficiency() {
        return averageEfficiency;
    }

    public void setAverageEfficiency(String averageEfficiency) {
        BigDecimal b1 = new BigDecimal(averageEfficiency).setScale(2, RoundingMode.HALF_UP);
        this.averageEfficiency = b1.toString();
    }

    public String getTotalPower() {
        return totalPower;
    }

    public void setTotalPower(String totalPower) {
        BigDecimal bigDecimal = new BigDecimal(totalPower);
        this.totalPower = bigDecimal.divide(BigDecimal.valueOf(1000),2,BigDecimal.ROUND_HALF_UP).toString();
    }

    public String getTodayElectricity() {
        return todayElectricity;
    }

    public void setTodayElectricity(String todayElectricity) {
        BigDecimal bigDecimal = new BigDecimal(todayElectricity);
        this.todayElectricity = bigDecimal.divide(BigDecimal.valueOf(100),2,BigDecimal.ROUND_HALF_UP).toString();
    }

    public String getMonthElectricity() {
        return monthElectricity;
    }

    public void setMonthElectricity(String monthElectricity) {
        this.monthElectricity = (new BigDecimal(monthElectricity).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP)).toString();
    }

    public String getYearElectricity() {
        return yearElectricity;
    }

    public void setYearElectricity(String yearElectricity) {
        this.yearElectricity =  (new BigDecimal(yearElectricity).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP)).toString();
    }

    public String getTotalElectricity() {
        return totalElectricity;
    }

    public void setTotalElectricity(String totalElectricity) {
        this.totalElectricity =  (new BigDecimal(totalElectricity).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP)).toString();
    }

    public String getPlantCapacity() {
        return plantCapacity;
    }

    public void setPlantCapacity(String plantCapacity) {
        this.plantCapacity = (new BigDecimal(plantCapacity).divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP)).toString();
    }

    public String getTodayCo2() {
        return todayCo2;
    }

    public void setTodayCo2(String todayCo2) {
        BigDecimal b1 = new BigDecimal(todayCo2).divide(BigDecimal.valueOf(100));
        BigDecimal b2 = b1.multiply(BigDecimal.valueOf(0.997)).divide(BigDecimal.valueOf(1000),2,BigDecimal.ROUND_HALF_UP);
        this.todayCo2 = b2.toString();
    }

    public String getTotalCocal() {
        return totalCocal;
    }

    public void setTotalCocal(String totalCocal) {
        BigDecimal b1 = new BigDecimal(totalCocal).divide(BigDecimal.valueOf(100));
        BigDecimal b2 = b1.multiply(BigDecimal.valueOf(0.3025)).divide(BigDecimal.valueOf(1000),4,BigDecimal.ROUND_HALF_UP);
        this.totalCocal = b2.toString();
    }

    public String getTotalCo2() {
        return totalCo2;
    }

    public void setTotalCo2(String totalCo2) {
        BigDecimal b1 = new BigDecimal(totalCo2).divide(BigDecimal.valueOf(100));
        BigDecimal b2 = b1.multiply(BigDecimal.valueOf(0.997)).divide(BigDecimal.valueOf(1000),4,BigDecimal.ROUND_HALF_UP);
        this.totalCo2 = b2.toString();
    }

    public String getTreeNum() {
        return treeNum;
    }

    public void setTreeNum(String treeNum) {
        BigDecimal b1 = new BigDecimal(treeNum).divide(BigDecimal.valueOf(100));
        BigDecimal b2 = b1.multiply(BigDecimal.valueOf(0.832)).divide(BigDecimal.valueOf(1800),0,BigDecimal.ROUND_HALF_UP);
        this.treeNum = b2.toString();
    }
}
