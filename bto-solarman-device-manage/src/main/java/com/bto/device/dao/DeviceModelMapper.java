package com.bto.device.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.commons.pojo.entity.BtoDeviceModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/30 15:59
 */
@Mapper
public interface DeviceModelMapper extends BaseMapper<BtoDeviceModel> {

    /**
     * 查询设备的规格信息
     * @param deviceType
     * @return
     */
    List<BtoDeviceModel> getDeviceModel(@Param("deviceType") String deviceType);
}
