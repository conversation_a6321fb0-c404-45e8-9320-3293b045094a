package com.bto.device.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.commons.pojo.dto.AmmeterDTO;
import com.bto.commons.pojo.entity.BtoElectricityMeter;
import com.bto.commons.pojo.vo.InfraredMeterTable;
import com.bto.commons.pojo.vo.MeterReadingVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2024/8/21.
 */

@Mapper
public interface AmmeterMapper extends BaseMapper<BtoElectricityMeter> {
    List<BtoElectricityMeter> getAmmeterInfoById(@Param("ammeterDTO") AmmeterDTO ammeterDTO);

    String getPlantElectricity(@Param("plantUid") String plantUid, @Param("date") String date);

    List<BtoElectricityMeter> getLatestInfo(@Param("voltmeterId") List<String> voltmeterId);

    List<String> getMeterIdsById(@Param("plantUid")String plantUid);

    InfraredMeterTable getTableByPlantUid(@Param("ammeterDTO") AmmeterDTO ammeterDTO);

    List<MeterReadingVO> getPowerSupplyMeterInfo(@Param("ammeterDTO") AmmeterDTO ammeterDTO);
}
