<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.device.dao.AmmeterMapper">
    <select id="getAmmeterInfoById" resultType="com.bto.commons.pojo.entity.BtoElectricityMeter">
        WITH w_infrared_meter AS (
        SELECT
        plant_uid,
        voltmeter_id,
        init_time,
        ROW_NUMBER() OVER ( PARTITION BY voltmeter_id ORDER BY init_time DESC ) id,
        round( total_impep / 100, 2 ) totalImpep,
        round( total_expep / 100, 2 ) totalExpep,
        round( sharp_peak_impep / 100, 2 ) sharpPeakImpep,
        round( sharp_peak_expep / 100, 2 ) sharpPeakExpep,
        round( peak_impep / 100, 2 ) peakImpep,
        round( peak_expep / 100, 2 ) peakExpep,
        round( normal_impep / 100, 2 ) normalImpep,
        round( normal_expep / 100, 2 ) normalExpep,
        round( valley_impep / 100, 2 ) valleyImpep,
        round( valley_expep / 100, 2 ) valleyExpep,
        apv,
        bpv,
        cpv
        FROM
        bto_infrared_meter
        <where>
            plant_uid = #{ammeterDTO.plantUid}
            <if test="ammeterDTO.collectTime!=null">
                <if test="ammeterDTO.collectTime.length() == 10">
                    AND DATE( init_time ) = #{ammeterDTO.collectTime}
                </if>
                <if test="ammeterDTO.collectTime.length() == 7">
                    AND DATE( init_time ) LIKE CONCAT(#{ammeterDTO.collectTime},'%')
                </if>
            </if>
        </where>
        ) SELECT
        t1.*,
        CASE
        t2.meter_type
        WHEN 0 THEN
        '供电电表'
        WHEN 1 THEN
        '光伏电表' ELSE '未知电表类型'
        END AS meterType
        FROM
        w_infrared_meter t1
        LEFT JOIN bto_device t2 ON t1.voltmeter_id = t2.device_id
        WHERE
        t1.id = 1
        AND t2.is_deleted = 0
    </select>
    <select id="getPlantElectricity" resultType="java.lang.String">
        SELECT SUM(electricity)
        FROM bto_plant_day
        WHERE plant_uid = #{plantUid}
        <![CDATA[
          AND DATE(collect) <= #{date}
        ]]>
    </select>
    <select id="getLatestInfo" resultType="com.bto.commons.pojo.entity.BtoElectricityMeter">
        WITH t_rank AS (SELECT *,
        RANK() OVER ( PARTITION BY cabinet_id ORDER BY collect_time DESC ) AS `rank`
        FROM bto_infrared_meter)
        SELECT *
        FROM t_rank
        <where>
            AND `rank` = 1
            <if test="query.cabinetId!=null and query.cabinetId.size()>0">
                and cabinet_id IN
                <foreach collection="query.cabinetId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY collect_time desc
    </select>
    <select id="getMeterIdsById" resultType="java.lang.String">
        SELECT
            device_id
        FROM
            bto_device
        WHERE
            is_deleted = 0
          AND device_type = 32
          AND meter_type = 0
        <if test="plantUid!=null">
            AND plant_uid = #{plantUid}
        </if>
    </select>
    <select id="getTableByPlantUid" resultType="com.bto.commons.pojo.vo.InfraredMeterTable">
        SELECT
            t_latest.totalImpep - t_earliest.totalImpep AS totalElectricity,
            t_latest.totalExpep - t_earliest.totalExpep AS totalProduce,
            t_latest.sharpPeakImpep - t_earliest.sharpPeakImpep AS sharpPeak,
            t_latest.sharpPeakExpep - t_earliest.sharpPeakExpep AS sharpPeakExpep,
            t_latest.peakImpep - t_earliest.peakImpep AS peak,
            t_latest.peakExpep - t_earliest.peakExpep AS peakExpep,
            t_latest.normalImpep - t_earliest.normalImpep AS normal,
            t_latest.normalExpep - t_earliest.normalExpep AS normalExpep,
            t_latest.valleyImpep - t_earliest.valleyImpep AS valley,
            t_latest.valleyExpep - t_earliest.valleyExpep AS valleyExpep
        FROM
            (
                SELECT
                    plant_uid,
                    voltmeter_id,
                    init_time,
                    round( total_impep / 100, 2 ) totalImpep,
                    round( total_expep / 100, 2 ) totalExpep,
                    round( sharp_peak_impep / 100, 2 ) sharpPeakImpep,
                    round( sharp_peak_expep / 100, 2 ) sharpPeakExpep,
                    round( peak_impep / 100, 2 ) peakImpep,
                    round( peak_expep / 100, 2 ) peakExpep,
                    round( normal_impep / 100, 2 ) normalImpep,
                    round( normal_expep / 100, 2 ) normalExpep,
                    round( valley_impep / 100, 2 ) valleyImpep,
                    round( valley_expep / 100, 2 ) valleyExpep
                FROM
                    bto_infrared_meter
                WHERE
                    plant_uid = #{ammeterDTO.plantUid}
                  AND voltmeter_id = #{ammeterDTO.voltmeterId}
                  AND DATE( init_time ) BETWEEN #{ammeterDTO.lastCollectTime}
		AND #{ammeterDTO.collectTime}
        ORDER BY
            init_time ASC
            LIMIT 1
            ) AS t_earliest,
            (
        SELECT
            plant_uid,
            voltmeter_id,
            init_time,
            round( total_impep / 100, 2 ) totalImpep,
            round( total_expep / 100, 2 ) totalExpep,
            round( sharp_peak_impep / 100, 2 ) sharpPeakImpep,
            round( sharp_peak_expep / 100, 2 ) sharpPeakExpep,
            round( peak_impep / 100, 2 ) peakImpep,
            round( peak_expep / 100, 2 ) peakExpep,
            round( normal_impep / 100, 2 ) normalImpep,
            round( normal_expep / 100, 2 ) normalExpep,
            round( valley_impep / 100, 2 ) valleyImpep,
            round( valley_expep / 100, 2 ) valleyExpep
        FROM
            bto_infrared_meter
        WHERE
            plant_uid = #{ammeterDTO.plantUid}
          AND voltmeter_id = #{ammeterDTO.voltmeterId}
          AND DATE( init_time ) BETWEEN #{ammeterDTO.lastCollectTime}
          AND #{ammeterDTO.collectTime}
        ORDER BY
            init_time DESC
            LIMIT 1
            ) AS t_latest
        WHERE
            t_earliest.plant_uid = t_latest.plant_uid
    </select>
    <select id="getPowerSupplyMeterInfo" resultType="com.bto.commons.pojo.vo.MeterReadingVO">
        CALL proc_electricity_bill (#{ammeterDTO.plantUid},#{ammeterDTO.lastCollectTime},#{ammeterDTO.collectTime})
    </select>
</mapper>