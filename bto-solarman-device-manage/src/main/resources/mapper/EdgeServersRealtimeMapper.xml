<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.device.dao.EdgeServerRealtimeMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bpb.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bpb.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <!--获取运维器列表-->
    <resultMap id="EdgeServerVOMap" type="com.bto.commons.pojo.dto.EdgeServerDTO">
        <result column="plant_uid" property="plantUid" />
        <result column="imei" property="imei" />
        <result column="collect_date" property="collectDate"/>
        <result column="generation_electricity" property="generationElectricity"/>
        <result column="use_electricity" property="useElectricity"/>
        <result column="deprecated_generate_electricity" property="deprecatedGenerateElectricity"/>
        <result column="buy_electricity" property="buyElectricity"/>
        <result column="sell_electricity" property="sellElectricity"/>
        <result column="apv" property="apv"/>
        <result column="bpv" property="bpv"/>
        <result column="cpv" property="cpv"/>
        <result column="dpv" property="dpv"/>
        <result column="impep" property="impep"/>
        <result column="expep" property="expep"/>
        <result column="state" property="state"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="plantName" property="plantName"/>
        <result column="status" property="status"/><!--运维器启动状态-->
        <result column="enable" property="enable"/><!--运维器运行状态-->
        <result column="device_id" property="edgeServerSN"/>
    </resultMap>
    <select id="getEdgeServers" resultMap="EdgeServerVOMap">
        select
        besr.*,bd.enable,bd.status,bpb.plant_name plantName,bd.device_id
        from bto_edge_server_realtime besr
        left join bto_device bd on besr.plant_uid= bd.plant_uid
        left join bto_plant_base bpb on besr.plant_uid= bpb.plant_uid
        <where>
            <trim suffixOverrides="and | or">
                <include refid="userInfo" />
                and bd.device_type=2 and bd.is_deleted=0 and besr.collect_date=#{edgeServerDTO.collectDate} and
                <if test="edgeServerDTO.plantName!=null and edgeServerDTO.plantName!='' ">
                    bpb.plant_name like concat('%',#{edgeServerDTO.plantName},'%') and
                </if>
                <if test="edgeServerDTO.imei!=null and edgeServerDTO.imei!= '' ">
                    bd.imei = #{edgeServerDTO.imei} and
                </if>
                <if test="edgeServerDTO.edgeServerSN!='' and edgeServerDTO.edgeServerSN!=null">
                    bd.device_id like concat('%',#{edgeServerDTO.edgeServerSN}) and
                </if>
                <if test="edgeServerDTO.generationElectricity!='' and edgeServerDTO.generationElectricity!=null">
                    besr.generation_electricity=#{edgeServerDTO.generationElectricity} and
                </if>
                <if test="edgeServerDTO.plantUid!='' and edgeServerDTO.plantUid!=null">
                    bpb.plant_uid = #{edgeServerDTO.plantUid} and
                </if>
                <if test="edgeServerDTO.status!=null and edgeServerDTO.status.size()>0">
                    bd.status=#{edgeServerDTO.status} and
                </if>
                <if test="edgeServerDTO.enable!=null and edgeServerDTO.enable.size()>0">
                    bd.enable=#{edgeServerDTO.enable} and
                </if>
            </trim>
        </where>
    </select>

    <!--获取用户买卖电量详情-->
    <select id="getUserBuySellInfo" resultType="HashMap">
        select
        plant_uid,
        buy_electricity,
        deprecated_generate_electricity,
        generation_electricity,
        collect_date
        from bto_edge_server_realtime
        <where>
            <trim suffixOverrides="and | or">
                plant_uid= #{plantUid} and
                <if test="startTime!='' and startTime!=null">
                    collect_date &gt;= #{startTime} and
                </if>
                <if test="endTime!=null and endTime!=''">
                    collect_date &lt;=#{endTime} and
                </if>
            </trim>
        </where>
        group by collect_date
    </select>

    <resultMap id="EdgeServerRealTimeMap" type="com.bto.commons.pojo.entity.EdgeServerRealtime">
        <result column="plant_uid" property="plantUid"/>
        <result column="generationElectrityStats" property="generationElectricity"/>
        <result column="useElectricityStats" property="useElectricity"/>
        <result column="deprecatedGenerateElectricityStats" property="deprecatedGenerateElectricity"/>
        <result column="buyElectricityStats" property="buyElectricity"/>
        <result column="sellElectricityStats" property="sellElectricity"/>
        <result column="collectDate" property="collectDate"/>
        <result column="maxApv" property="apv"/>
        <result column="maxBpv" property="bpv"/>
        <result column="maxCpv" property="cpv"/>
        <result column="maxDpv" property="dpv"/>
    </resultMap>
    <select id="getEdgeServerRealTimeStatsByDay" resultMap="EdgeServerRealTimeMap">
        SELECT
            plant_uid,
            convert(sum(generation_electricity/100),decimal(12, 2)) generationElectrityStats,
        convert(sum(use_electricity/100),decimal(12, 2)) useElectricityStats,
        convert(sum(deprecated_generate_electricity/100),decimal(12, 2)) deprecatedGenerateElectricityStats,
        convert(sum(buy_electricity/100),decimal(12, 2)) buyElectricityStats,
        convert(sum(sell_electricity/100),decimal(12, 2)) sellElectricityStats,
            collect_date  collectDate,
            max(apv) maxApv,
            max(bpv) maxBpv,
            max(cpv) maxCpv,
            max(dpv) maxDpv
        FROM
            bto_edge_server_realtime
        <where>
            <if test="imei!=null and imei != ''">
                and imei=#{imei}
            </if>
            <if test="plantUid!=null and plantUid != ''">
                and plant_uid=#{plantUid}
            </if>
            <if test="startTime!=null and startTime !=''">
                and collect_date &gt;=#{startTime}
            </if>
            <if test="endTime!=null and endTime!='' ">
                and  collect_date &lt;=#{endTime}
            </if>
            GROUP BY collectDate;
        </where>
    </select>


    <select id="getEdgeServerRealTimeStatsByMonth" resultMap="EdgeServerRealTimeMap">
        SELECT
        plant_uid, -- 电站Uid
        convert(sum(generation_electricity/100),decimal(12, 2)) generationElectrityStats,-- 发电量
        convert(sum(use_electricity/100),decimal(12,2)) useElectricityStats, -- 用电量
        convert(sum(deprecated_generate_electricity/100),decimal(12,2)) deprecatedGenerateElectricityStats, -- 自发自用
        convert(sum(buy_electricity/100),decimal(12,2)) buyElectricityStats, -- 买电量
        convert(sum(sell_electricity/100),decimal(12,2)) sellElectricityStats, -- 卖电量
        LEFT(collect_date,7) collectDate,   -- 日期
        max(apv) maxApv,
        max(bpv) maxBpv,
        max(cpv) maxCpv,
        max(dpv) maxDpv
        FROM
        bto_edge_server_realtime
        <where>
            <if test="imei!=null and imei != ''">
                and imei=#{imei}
            </if>
            <if test="plantUid!=null and plantUid != ''">
                and plant_uid = #{plantUid}
            </if>
            <if test="startTime!=null and startTime !=''">
                and  collect_date &gt;=#{startTime}
            </if>
            <if test="endTime!=null and endTime!='' ">
                and  collect_date &lt;=#{endTime}
            </if>
            GROUP BY collectDate;-- 运维器图表（年）
        </where>
    </select>

    <select id="getEdgeServerRealTimeStatsByYear" resultMap="EdgeServerRealTimeMap">
        SELECT
        plant_uid, -- 电站Uid
        convert(sum(generation_electricity/100),decimal(12,2)) generationElectrityStats,-- 发电量
        convert(sum(use_electricity/100),decimal(12,2)) useElectricityStats, -- 用电量
        convert(sum(deprecated_generate_electricity/100),decimal(12,2)) deprecatedGenerateElectricityStats, -- 自发自用
        convert(sum(buy_electricity/100),decimal(12,2)) buyElectricityStats, -- 买电量
        convert(sum(sell_electricity/100),decimal(12,2)) sellElectricityStats, -- 卖电量
        LEFT(collect_date,4) collectDate,   -- 日期
        max(apv) maxApv,
        max(bpv) maxBpv,
        max(cpv) maxCpv,
        max(dpv) maxDpv
        FROM
        bto_edge_server_realtime
        <where>
            <if test="imei!=null and imei != ''">
                and imei=#{imei}
            </if>
            <if test="plantUid!=null and plantUid != ''">
               and plant_uid=#{plantUid}
            </if>
            <if test="startTime!=null and startTime !=''">
                and collect_date &gt;=#{startTime}
            </if>
            <if test="endTime!=null and endTime!='' ">
                and collect_date &lt;=#{endTime}
            </if>
            GROUP BY collectDate;-- 运维器图表（年）
        </where>
    </select>

    <select id="getEdgeServerSignal" resultType="string">
        SELECT signal_strength
        FROM v_parameter v1
                 LEFT JOIN v_inverter_electricity v2 ON v1.inverter_sn = v2.inverter_sn
        WHERE imei = #{imei}
          AND is_deleted &lt;&gt; 1
        GROUP BY imei
    </select>
</mapper>