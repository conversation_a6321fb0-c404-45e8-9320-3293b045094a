package com.bto.statistics.dao;

import com.bto.commons.pojo.dto.StatisticsQueryDTO;
import com.bto.commons.pojo.vo.PlantAllStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 电站全量统计Mapper接口
 * 提供电站全量统计数据的查询功能，包括发电、用电、储电、收益等各个维度的统计分析
 * 
 * <AUTHOR>
 */
@Mapper
public interface PlantAllStatisticsMapper {

    // ------------------------- 发电统计 -------------------------
    /**
     * 查询日发电功率数据
     * 获取指定条件下的每日发电功率统计数据，用于功率趋势分析
     * 
     * @param dto 统计查询条件，包含时间范围、电站ID等筛选条件
     * @return 日发电功率数据列表，包含每日的功率统计信息
     */
    List<PlantAllStatisticsVO.GenerationPowerVO> selectGenerationPowerDay(@Param("dto") StatisticsQueryDTO dto);

    /**
     * 查询发电量统计数据
     * 获取指定条件下的发电量累计统计数据
     * 
     * @param dto 统计查询条件，包含时间范围、电站ID等筛选条件
     * @return 发电量统计数据列表，包含累计发电量信息
     */
    List<PlantAllStatisticsVO.GenerationEnergyVO> selectGenerationEnergy (@Param("dto") StatisticsQueryDTO dto);

    /**
     * 查询日发电量数据
     * 获取指定条件下的每日发电量统计数据
     * 
     * @param dto 统计查询条件，包含时间范围、电站ID等筛选条件
     * @return 日发电量数据，以字符串形式返回
     */
    String selectGenerationDay(@Param("dto") StatisticsQueryDTO dto);

    // ------------------------- 用电统计 -------------------------
    /**
     * 查询日用电功率数据
     * 获取指定条件下的每日用电功率统计数据
     * 
     * @param dto 统计查询条件，包含时间范围、电站ID等筛选条件
     * @return 日用电功率数据列表，包含每日的用电功率统计信息
     */
    List<PlantAllStatisticsVO.LoadPowerDayVO> selectLoadPowerDay(@Param("dto") StatisticsQueryDTO dto);

    /**
     * 查询用电量统计数据
     * 获取指定条件下的用电量累计统计数据
     * 
     * @param dto 统计查询条件，包含时间范围、电站ID等筛选条件
     * @return 用电量统计数据列表，包含累计用电量信息
     */
    List<PlantAllStatisticsVO.LoadEnergyVO> selectLoadEnergy(@Param("dto") StatisticsQueryDTO dto);

    /**
     * 查询日用电量数据
     * 获取指定条件下的每日用电量统计数据
     * 
     * @param dto 统计查询条件，包含时间范围、电站ID等筛选条件
     * @return 日用电量数据，以Map形式返回，包含用电相关统计信息
     */
    Map<String, Object> selectLoadDay(@Param("dto") StatisticsQueryDTO dto);

    // ------------------------- 储电统计 -------------------------
    /**
     * 查询日储电功率数据
     * 获取指定条件下的每日储电功率统计数据
     * 
     * @param dto 统计查询条件，包含时间范围、电站ID等筛选条件
     * @return 日储电功率数据列表，包含每日的储电功率统计信息
     */
    List<PlantAllStatisticsVO.BatteryPowerVO> selectBatteryPowerDay(@Param("dto") StatisticsQueryDTO dto);

    /**
     * 查询储电量统计数据
     * 获取指定条件下的储电量累计统计数据
     * 
     * @param dto 统计查询条件，包含时间范围、电站ID等筛选条件
     * @return 储电量统计数据列表，包含累计储电量信息
     */
    List<PlantAllStatisticsVO.BatteryEnergyVO> selectBatteryEnergy (@Param("dto") StatisticsQueryDTO dto);

    /**
     * 查询日储电量数据
     * 获取指定条件下的每日储电量统计数据
     * 
     * @param dto 统计查询条件，包含时间范围、电站ID等筛选条件
     * @return 日储电量数据，包含储电相关统计信息
     */
    PlantAllStatisticsVO.BatteryEnergyVO selectBatteryDay(@Param("dto") StatisticsQueryDTO dto);

    // ------------------------- 收益统计 -------------------------
    /**
     * 查询收益统计数据
     * 获取指定条件下的收益累计统计数据
     * 
     * @param dto 统计查询条件，包含时间范围、电站ID等筛选条件
     * @return 收益统计数据列表，包含累计收益信息
     */
    List<PlantAllStatisticsVO.RevenueVO> queryRevenueStatistics (@Param("dto") StatisticsQueryDTO dto);
}