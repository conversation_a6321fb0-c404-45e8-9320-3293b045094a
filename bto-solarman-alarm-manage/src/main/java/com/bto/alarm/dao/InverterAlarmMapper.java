package com.bto.alarm.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.commons.pojo.dto.InverterAlarmAggrDTO;
import com.bto.commons.pojo.dto.InverterAlarmQueryDTO;
import com.bto.commons.pojo.dto.PlantEvaluationQuery;
import com.bto.commons.pojo.entity.InverterAlarm;
import com.bto.commons.pojo.vo.InverterAlarmInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.google.common.collect.ArrayListMultimap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/20 15:39
 */
@Mapper
public interface InverterAlarmMapper extends BaseMapper<InverterAlarm> {
    /**
     * 获取逆变器告警信息
     *
     * @param query
     * @param userInfo
     * @return
     */
    List<InverterAlarmInfoVO> getInverterAlarmInfo(@Param("query") InverterAlarmQueryDTO query
            , @Param("userInfo") RequireParamsDTO userInfo);

    /**
     * 获取逆变器告警聚合分类信息
     *
     * @param inverterAlarmAggrDTO
     */
    List<HashMap<String, Object>> getInverterAlarmInfoAggr(@Param("inverterAlarmAggrVO") InverterAlarmAggrDTO inverterAlarmAggrDTO
            , @Param("userInfo") RequireParamsDTO userInfo);


    List<String> getInverterIdByProjectId(@Param("userInfo") RequireParamsDTO userInfo,@Param("projectId") String projectId);

    List<Map<String, Object>> getInverterAlarmInfoByProjectId(@Param("query") PlantEvaluationQuery query);
}
