spring:
  cloud:
    nacos:
      config:
        namespace: d7b28a7f-9929-4129-a774-202be7f0bd61
      discovery:
        namespace: d7b28a7f-9929-4129-a774-202be7f0bd61
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging:
  config: classpath:log4j2-dev.xml
seata:
  tx-service-group: ${spring.application.name}-group
  service:
    grouplist:
      solarman: 172.16.10.168:8091
    vgroup-mapping:
      solarman-engineer-group: solarman