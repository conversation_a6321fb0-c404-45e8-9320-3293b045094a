package com.bto.app.pojo.dto;

import com.bto.commons.pojo.dto.UserInfoDTO;
import com.bto.commons.pojo.entity.ContractInfoEntity;
import com.bto.commons.pojo.entity.DeviceDesignEntity;
import com.bto.commons.pojo.entity.PvNumDesignEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/18 16:01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RegisterPlantDTO {

    @ApiModelProperty(value = "合同信息")
    private ContractInfoEntity contractInfo;

    @ApiModelProperty(value = "用户信息")
    private UserInfoDTO userInfoDTO;

    @ApiModelProperty(value = "建站模式：0-> 一键建站  1->传统建站",required = true)
    private Integer type;

    @ApiModelProperty(value = "设备信息")
    private List<DeviceDesignEntity> devices;

    @ApiModelProperty(value = "逆变器组件信息")
    private List<PvNumDesignEntity> inverterComponents;

}
