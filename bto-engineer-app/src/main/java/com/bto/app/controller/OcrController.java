package com.bto.app.controller;

import cn.hutool.json.JSONUtil;
import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.RecognizeGeneralRequest;
import com.aliyun.ocr_api20210707.models.RecognizeGeneralResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.bto.commons.constant.Constant;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> by zhb on 2024/12/11.
 */
@RestController
@RequestMapping("ocr")
@RequiredArgsConstructor
@Api(tags = "文字识别")
@Slf4j
public class OcrController {

    /**
     * 图片文字识别
     * @param file 图片文件
     * @return 文字内容
     */
    @PostMapping
    public Object ocr(@RequestBody MultipartFile file) throws Exception {
        Client client = createClient();
        RecognizeGeneralRequest recognizeGeneralRequest = new RecognizeGeneralRequest()
                .setBody(file.getInputStream());
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            RecognizeGeneralResponse recognizeBasicResponse = client.recognizeGeneralWithOptions(recognizeGeneralRequest, runtime);
            String data = recognizeBasicResponse.body.getData();
            return JSONUtil.parseObj(data).get("content");
        } catch (Exception error) {
            TeaException exception = new TeaException(error.getMessage(), error);
            log.error(exception.getMessage());
            log.error(exception.getData().get("Recommend").toString());
        }
        return "识别错误，请手动填写";
    }

    public static Client createClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(Constant.ALIYUN_ACCESS_KEY_ID)
                .setAccessKeySecret(Constant.ALIYUN_ACCESS_KEY_SECRET);
        config.endpoint = "ocr-api.cn-hangzhou.aliyuncs.com";
        return new Client(config);
    }

}